"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Home } from "lucide-react";
import Header from "@/components/dashboard-components/Header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import LexicalEditor from "@/components/ui/lexical-editor";
import { EditorState } from 'lexical';
import {
    Archive,
    ArrowDownToLine,
    ChevronLeft,
    Eye,
    MoreVertical,
    Search,
    Timer,
    Trash2,
    Send,
    Paperclip,
    LockKeyhole,
    CheckCheck,
    LogOut,
    X
} from "lucide-react";
import { mockConversationData, getMessagesByConversationId } from "@/lib/mockData/conversationData";
import { ConversationData } from "@/lib/types";
import { apiClient } from "@/lib/utils/apiClient";
import logger from "@/lib/utils/logger";

const ConversationItem = ({
    conversation,
    isActive,
    onClick}: {
    conversation: ConversationData;
    isActive: boolean;
    onClick: () => void;
    onArchive: () => void;
    onDelete: () => void;
}) => {
    const baseClasses = "flex flex-col justify-between group cursor-pointer transition-all duration-200 p-3 border-b border-gray-100 hover:bg-gray-50";
    const activeClasses = isActive ? "bg-[#ECF4E9] border-l-4 border-l-[#1E4841]" : "bg-white";
    const unreadClasses = conversation.isUnread ? "font-medium" : "";
    const classNames = `${baseClasses} ${activeClasses} ${unreadClasses}`.trim();
    
    const avatarBg = conversation.avatarBg || "bg-[#BBF49C]";

    return (
        <div
            className={classNames}
            onClick={onClick}
            role="button"
            tabIndex={0}
            aria-label={`Conversation with ${conversation.name} about ${conversation.caseId}`}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onClick();
                }
            }}
        >
            <div className="flex items-center gap-3 mb-2">
                <div className="relative">
                    <div className={`h-10 w-10 rounded-full flex items-center justify-center ${avatarBg}`}>
                        <span className="text-sm font-medium text-[#1E4841]">
                            {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                        </span>
                    </div>
                    {conversation.isOnline && (
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                    )}
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <p className="text-sm font-medium text-[#111827] truncate">
                                {conversation.name}
                            </p>
                            {conversation.isOnline && (
                                <span className="text-xs text-green-600 font-medium">Online</span>
                            )}
                        </div>
                        <div className="flex items-center gap-2">
                            <p className="text-xs text-[#6B7280]">{conversation.time}</p>
                            {conversation.isUnread && (
                                <div className="w-2.5 h-2.5 bg-[#EF4444] rounded-full"></div>
                            )}
                        </div>
                    </div>
                    <p className="text-xs text-[#6B7280] mb-1">{conversation.caseId}</p>
                </div>
            </div>
            <div className="flex items-center justify-between">
                <p className="text-sm text-[#4B5563] truncate flex-1 mr-2">
                    {conversation.isTyping ? (
                        <span className="text-green-600 italic">Typing...</span>
                    ) : (
                        conversation.lastMessage
                    )}
                </p>
                <div className="flex items-center gap-1">
                    <LockKeyhole className="w-3 h-3 text-[#1E4841]" />
                </div>
            </div>
        </div>
    );
};

export default function SecureMessage() {
    const router = useRouter();
    const [activeConversationId, setActiveConversationId] = useState<string>("");
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [newMessage, setNewMessage] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [autoLogoutTime, setAutoLogoutTime] = useState<number>(15 * 60);
    const [showTypingIndicator, setShowTypingIndicator] = useState<boolean>(false);
    const [showAutoLogout, setShowAutoLogout] = useState<boolean>(false);
    const [conversationData, setConversationData] = useState(() => [...mockConversationData]);
    const [refreshTrigger, setRefreshTrigger] = useState(0);
    const [sentMessages, setSentMessages] = useState<Record<string, Array<{id: string, content: string, timestamp: string, html?: string, attachments?: File[]}>>>({});
    const fileInputRef = useRef<HTMLInputElement>(null);
    const messagesContainerRef = useRef<HTMLDivElement>(null);
    const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
    const [clearTrigger, setClearTrigger] = useState(0);

    const resetAutoLogoutTimer = useCallback(() => {
        setAutoLogoutTime(15 * 60);
        setShowAutoLogout(false);
    }, []);

    // Refresh conversation data from persistent source
    useEffect(() => {
        setConversationData([...mockConversationData]);
    }, [refreshTrigger]);

    // Refresh data when tab becomes visible (user switches back)
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden) {
                setRefreshTrigger(prev => prev + 1);
            }
        };

        const handleFocus = () => {
            setRefreshTrigger(prev => prev + 1);
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        window.addEventListener('focus', handleFocus);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('focus', handleFocus);
        };
    }, []);

    const scrollToBottom = useCallback(() => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    }, []);

    useEffect(() => {
        let inactivityTimeout: NodeJS.Timeout;

        const handleUserActivity = () => {
            resetAutoLogoutTimer();

            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }

            inactivityTimeout = setTimeout(() => {
                setShowAutoLogout(true);
            }, 2 * 60 * 1000);
        };

        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

        events.forEach(event => {
            document.addEventListener(event, handleUserActivity, true);
        });

        handleUserActivity();

        return () => {
            events.forEach(event => {
                document.removeEventListener(event, handleUserActivity, true);
            });
            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }
        };
    }, [resetAutoLogoutTimer]);

    useEffect(() => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    }, [sentMessages, showTypingIndicator, activeConversationId]);

    useEffect(() => {
        if (!showAutoLogout) return;

        const timer = setInterval(() => {
            setAutoLogoutTime(prev => {
                if (prev <= 1) {
                    router.push('/logout');
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [router, showAutoLogout]);

    const formatAutoLogoutTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    const filteredConversations = conversationData.filter(conv =>
        conv.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        conv.caseId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        conv.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const activeConversation = conversationData.find(conv => conv.id === activeConversationId);
    const conversationMessages = activeConversation ? getMessagesByConversationId(activeConversation.id) : [];

    const handleConversationSelect = async (conversationId: string) => {
        setActiveConversationId(conversationId);

        // Mark conversation as read and update the sidebar counter
        try {
            await apiClient.post('/api/conversations/read-status', {
                conversationId: conversationId
            });
        } catch (error) {
            console.error('Error marking conversation as read:', error);
        }

        // Refresh the conversation data to reflect the persistent changes
        setRefreshTrigger(prev => prev + 1);
    };

    const handleSendMessage = async () => {
        if (!newMessage.trim()) return;

        const messageContent = newMessage.trim();
        const timestamp = new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        setIsLoading(true);
        setShowTypingIndicator(false);

        const newSentMessage = {
            id: `sent_${Date.now()}`,
            content: messageContent,
            timestamp: timestamp,
            attachments: attachedFiles.length > 0 ? [...attachedFiles] : undefined
        };
        setSentMessages(prev => ({
            ...prev,
            [activeConversationId]: [...(prev[activeConversationId] || []), newSentMessage]
        }));

        setNewMessage("");
        setAttachedFiles([]);
        handleClearEditor();

        await new Promise(resolve => setTimeout(resolve, 1000));

        setShowTypingIndicator(true);
        setTimeout(() => {
            setShowTypingIndicator(false);
        }, 3000);

        setIsLoading(false);
        resetAutoLogoutTimer();

        setTimeout(() => {
            scrollToBottom();
        }, 100);
    };

    const handleArchiveConversation = () => {
        if (!activeConversation) return;
        // Archive functionality is now handled by ConversationItem component with AlertDialog
        console.log(`Archiving conversation ${activeConversation.id}`);
        setConversationData(prev => prev.filter(conv => conv.id !== activeConversation.id));
        setActiveConversationId("");
    };

    const handleDeleteConversation = () => {
        if (!activeConversation) return;
        // Delete functionality is now handled by ConversationItem component with AlertDialog
        console.log(`Deleting conversation ${activeConversation.id}`);
        setConversationData(prev => prev.filter(conv => conv.id !== activeConversation.id));
        setActiveConversationId("");
    };

    const handleArchive = (conversationId: string) => {
        setConversationData(prev => prev.filter(conv => conv.id !== conversationId));
        if (activeConversationId === conversationId) {
            setActiveConversationId("");
        }
        logger.info(`Archived conversation ${conversationId}`);
    };
    const handleDelete = (conversationId: string) => {
        setConversationData(prev => prev.filter(conv => conv.id !== conversationId));
        if (activeConversationId === conversationId) {
            setActiveConversationId("");
        }
        logger.info(`Deleted conversation ${conversationId}`);
    };

    const handleEditorChange = (newEditorState: EditorState) => {
        newEditorState.read(() => {
            const root = newEditorState._nodeMap.get('root');
            if (root) {
                setNewMessage(root.getTextContent());
            }
        });

        resetAutoLogoutTimer();
    };

    const handleEditorKeyDown = (event: KeyboardEvent): boolean => {
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            event.stopPropagation();
            handleSendMessage();
            return true;
        }
        return false;
    };

    const handleClearEditor = () => {
        setClearTrigger(prev => prev + 1);
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files && files.length > 0) {
            setAttachedFiles(prev => [...prev, ...Array.from(files)]);
        }
    };

    const removeAttachedFile = (index: number) => {
        setAttachedFiles(prev => prev.filter((_, i) => i !== index));
    };

    return (
        <div className="w-full h-screen flex flex-col">
            <Header />
            <main id="main-content" className="flex-1 bg-white" aria-label="Secure message management">
                <header className="px-4 sm:px-6 py-4 w-full flex flex-col gap-4 bg-white border-b">
                    <div className="flex-1">
                        <Breadcrumb>
                            <BreadcrumbList>
                                <BreadcrumbItem>
                                    <BreadcrumbLink href="/dashboard" className="flex items-center gap-1">
                                        <Home className="w-4 h-4" />
                                        Dashboard
                                    </BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbSeparator />
                                <BreadcrumbItem>
                                    <BreadcrumbPage>Secure Messages</BreadcrumbPage>
                                </BreadcrumbItem>
                            </BreadcrumbList>
                        </Breadcrumb>
                        <h1 className="text-2xl sm:text-2xl font-semibold text-[#242E2C]">Secure Case Messaging</h1>
                        <p className="text-sm text-[#6B7280] mt-1">End-to-end encrypted communication with investigators</p>
                    </div>
                </header>

                <div className="flex-1 flex flex-col lg:flex-row h-[calc(100vh-180px)]">
                    <div className="w-full lg:w-1/3 xl:w-1/4 border-r border-gray-200 flex flex-col h-full">
                        <div className="p-4 border-b border-gray-100">
                            <div className="relative mb-4">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#6B7271] w-4 h-4" />
                                <Input
                                    name="searchMessages"
                                    aria-label="Search conversations"
                                    placeholder="Search conversations..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-10 py-3 border-2 border-[#D1D5DB] text-[#6B7271] text-sm font-medium focus:border-[#1E4841] focus:ring-[#1E4841]"
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <h2 className="text-sm font-medium text-[#111827]">Conversations</h2>
                                <Badge variant="secondary" className="bg-[#ECF4E9] text-[#1E4841] text-xs">
                                    {filteredConversations.filter(c => c.isUnread).length} unread
                                </Badge>
                            </div>
                        </div>

                        <div className="flex-1 overflow-y-auto">
                            {filteredConversations.length === 0 ? (
                                <div className="p-4 text-center">
                                    <p className="text-sm text-[#6B7280]">No conversations found</p>
                                </div>
                            ) : filteredConversations.map(conversation => (
                                <ConversationItem
                                    key={conversation.id}
                                    conversation={conversation}
                                    isActive={conversation.id === activeConversationId}
                                    onClick={() => handleConversationSelect(conversation.id)}
                                    onArchive={() => handleArchive(conversation.id)}
                                    onDelete={() => handleDelete(conversation.id)}
                                />
                            ))}
                        </div>
                    </div>
                    <div className="flex-1 lg:flex-2 xl:flex-3 flex flex-col h-full">
                        {activeConversation ? (
                            <div className="flex flex-col h-full bg-white">
                                <div className="p-4 border-b flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 bg-gray-50">
                                    <div className="flex items-center gap-3">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="lg:hidden p-1"
                                            aria-label="Back to conversations"
                                        >
                                            <ChevronLeft className="w-4 h-4 text-[#6B7271]" />
                                        </Button>
                                        <div className="relative">
                                            <div className={`h-10 w-10 rounded-full flex items-center justify-center ${activeConversation.avatarBg || "bg-[#BBF49C]"}`}>
                                                <span className="text-sm font-medium text-[#1E4841]">
                                                    {activeConversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                </span>
                                            </div>
                                            {activeConversation.isOnline && (
                                                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                                            )}
                                        </div>
                                        <div>
                                            <div className="flex items-center gap-2">
                                                <p className="text-base font-medium text-[#111827]">{activeConversation.name}</p>
                                                {activeConversation.isOnline && (
                                                    <span className="text-xs text-green-600 font-medium">Online</span>
                                                )}
                                            </div>
                                            <div className="flex items-center gap-2 text-xs text-[#6B7280]">
                                                <span>{activeConversation.caseId}</span>
                                                <span className="text-xs">•</span>
                                                <LockKeyhole className="w-3 h-3" />
                                                <span>End-to-end encrypted</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={handleArchiveConversation}
                                            aria-label="Archive conversation"
                                        >
                                            <Archive className="w-4 h-4 text-[#6B7271]" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={handleDeleteConversation}
                                            aria-label="Delete conversation"
                                        >
                                            <Trash2 className="w-4 h-4 text-[#6B7271]" />
                                        </Button>
                                        <Button variant="ghost" size="sm" aria-label="More options">
                                            <MoreVertical className="w-4 h-4 text-[#6B7271]" />
                                        </Button>
                                    </div>
                                </div>
                                <div
                                    ref={messagesContainerRef}
                                    className="flex-1 overflow-y-auto p-4 space-y-4"
                                >
                                    <div className="text-center">
                                        <p className="text-xs text-[#4B5563] bg-gray-100 px-3 py-1 rounded-full inline-block">
                                            May 15, 2025
                                        </p>
                                    </div>

                                    <div className="flex justify-center">
                                        <div className="flex items-center gap-2 text-xs text-[#1E4841] bg-[#ECF4E9] px-4 py-2 rounded-lg max-w-2xl text-center">
                                            <LockKeyhole className="w-4 h-4" />
                                            <span>This conversation is end-to-end encrypted. Only you and {activeConversation.name} can read these messages.</span>
                                        </div>
                                    </div>

                                    <div className="space-y-6">
                                        {conversationMessages.map((message) => (
                                            <div key={message.id}>
                                                {message.isFromUser ? (
                                                    <div className="flex items-start gap-3 justify-end">
                                                        <div className="flex flex-col gap-2 max-w-md">
                                                            <div className="flex items-center gap-2 justify-end">
                                                                <p className="text-xs text-[#6B7280]">{message.timestamp}</p>
                                                                <p className="text-sm font-medium text-[#111827]">You</p>
                                                            </div>
                                                            <div className="bg-[#ECF4E9] rounded-2xl rounded-tr-md p-3">
                                                                <p className="text-sm text-[#1F2937]">
                                                                    {message.content}
                                                                </p>
                                                            </div>
                                                            {message.attachments && message.attachments.length > 0 && (
                                                                <div className="mt-2">
                                                                    {message.attachments.map((attachment, index) => (
                                                                        <div key={index} className="bg-white border border-[#E5E7EB] rounded-lg p-3">
                                                                            <div className="flex items-center gap-3">
                                                                                <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
                                                                                    <Paperclip className="w-5 h-5 text-green-600" />
                                                                                </div>
                                                                                <div className="flex-1">
                                                                                    <p className="text-sm font-medium text-[#111827]">{attachment.fileName}</p>
                                                                                    <p className="text-xs text-[#6B7280]">{attachment.fileType} • {attachment.fileSize}</p>
                                                                                </div>
                                                                                <div className="flex items-center gap-2">
                                                                                    <Button variant="ghost" size="sm" aria-label="Download file">
                                                                                        <ArrowDownToLine className="w-4 h-4 text-[#6B7271]" />
                                                                                    </Button>
                                                                                    <Button variant="ghost" size="sm" aria-label="Preview file">
                                                                                        <Eye className="w-4 h-4 text-[#6B7271]" />
                                                                                    </Button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            )}
                                                            <div className="flex items-center gap-2 text-xs text-[#6B7280] justify-end">
                                                                <LockKeyhole className="w-3 h-3" />
                                                                <span>Encrypted</span>
                                                                <span>•</span>
                                                                <CheckCheck className="w-3 h-3 text-green-600" />
                                                                <span>Read</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <div className="flex items-start gap-3">
                                                        <div className={
  "h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0 " +
  (activeConversation.avatarBg || "bg-[#BBF49C]")
}>
  <span className="text-xs font-medium text-[#1E4841]">
    {activeConversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
  </span>
</div>
                                                        <div className="flex flex-col gap-2 max-w-md">
                                                            <div className="flex items-center gap-2">
                                                                <p className="text-sm font-medium text-[#111827]">{message.senderName}</p>
                                                                <p className="text-xs text-[#6B7280]">{message.timestamp}</p>
                                                            </div>
                                                            <div className="bg-[#F9FAFB] border border-[#E5E7EB] rounded-2xl rounded-tl-md p-3">
                                                                <p className="text-sm text-[#1F2937]">
                                                                    {message.content}
                                                                </p>
                                                            </div>
                                                            <div className="flex items-center gap-2 text-xs text-[#6B7280]">
                                                                <LockKeyhole className="w-3 h-3" />
                                                                <span>Encrypted</span>
                                                                <span>•</span>
                                                                <span>Delivered</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        ))}

                                        {(sentMessages[activeConversationId] || []).map((message) => (
                                            <div key={message.id} className="flex items-start gap-3 justify-end">
                                                <div className="flex flex-col gap-2 max-w-md">
                                                    <div className="flex items-center gap-2 justify-end">
                                                        <p className="text-xs text-[#6B7280]">{message.timestamp}</p>
                                                        <p className="text-sm font-medium text-[#111827]">You</p>
                                                    </div>
                                                    <div className="bg-[#ECF4E9] rounded-2xl rounded-tr-md p-3">
                                                        <div
                                                            className="text-sm text-[#1F2937]"
                                                            dangerouslySetInnerHTML={{
                                                                __html: message.html || message.content
                                                            }}
                                                        />
                                                        {message.attachments && message.attachments.length > 0 && (
                                                            <div className="mt-3 space-y-2">
                                                                {message.attachments.map((file, fileIndex) => (
                                                                    <div key={fileIndex} className="bg-white border border-[#E5E7EB] rounded-lg p-2">
                                                                        <div className="flex items-center gap-2">
                                                                            <div className="h-8 w-8 rounded-lg bg-green-100 flex items-center justify-center">
                                                                                <Paperclip className="w-4 h-4 text-green-600" />
                                                                            </div>
                                                                            <div className="flex-1">
                                                                                <p className="text-xs font-medium text-[#111827]">{file.name}</p>
                                                                                <p className="text-xs text-[#6B7280]">{(file.size / 1024).toFixed(1)} KB</p>
                                                                            </div>
                                                                            <div className="flex items-center gap-1">
                                                                                <Button variant="ghost" size="sm" className="p-1">
                                                                                    <ArrowDownToLine className="w-3 h-3 text-[#6B7271]" />
                                                                                </Button>
                                                                                <Button variant="ghost" size="sm" className="p-1">
                                                                                    <Eye className="w-3 h-3 text-[#6B7271]" />
                                                                                </Button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center gap-2 text-xs text-[#6B7280] justify-end">
                                                        <LockKeyhole className="w-3 h-3" />
                                                        <span>Encrypted</span>
                                                        <span>•</span>
                                                        <CheckCheck className="w-3 h-3 text-green-600" />
                                                        <span>Sent</span>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}

                                        {showTypingIndicator && (
                                            <div className="flex items-start gap-3">
                                                <div className={
      "h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0 " +
      (activeConversation.avatarBg || "bg-[#BBF49C]")
    }>
                                                  <span className="text-xs font-medium text-[#1E4841]">
                                                    {activeConversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                  </span>
                                                </div>
                                                <div className="bg-[#F9FAFB] border border-[#E5E7EB] rounded-2xl rounded-tl-md p-3">
                                                    <div className="flex items-center gap-1">
                                                        <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce"></div>
                                                        <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                                        <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="p-4 border-t bg-gray-50">

                                    {showAutoLogout && (
                                        <div className="flex items-center gap-2 text-xs text-[#FF2121] mb-3">
                                            <Timer className="w-4 h-4" />
                                            <span>Auto-logout in {formatAutoLogoutTime(autoLogoutTime)}</span>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => router.push('/logout')}
                                                className="ml-2 p-1 text-[#FF2121] hover:bg-red-50"
                                                aria-label="Logout now"
                                            >
                                                <LogOut className="w-3 h-3" />
                                            </Button>
                                        </div>
                                    )}

                                    {attachedFiles.length > 0 && (
                                        <div className="mb-3 flex flex-wrap gap-2">
                                            {attachedFiles.map((file, index) => (
                                                <Badge
                                                    key={index}
                                                    variant="secondary"
                                                    className="bg-[#ECF4E9] text-[#1E4841] px-3 py-1 flex items-center gap-2 max-w-[200px]"
                                                >
                                                    <Paperclip className="w-3 h-3" />
                                                    <span className="text-xs truncate">{file.name}</span>
                                                    <span className="text-xs opacity-70">({(file.size / 1024).toFixed(1)}KB)</span>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => removeAttachedFile(index)}
                                                        className="p-0 h-auto w-4 text-red-500 hover:text-red-700 ml-1"
                                                    >
                                                        <X className="w-3 h-3" />
                                                    </Button>
                                                </Badge>
                                            ))}
                                        </div>
                                    )}

                                    <Input
                                        ref={fileInputRef}
                                        type="file"
                                        multiple
                                        className="hidden"
                                        onChange={handleFileSelect}
                                        accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                                    />

                                    <div className="flex gap-2">
                                        <div className="flex-1">
                                            <LexicalEditor
                                                placeholder=""
                                                onChange={handleEditorChange}
                                                onKeyDown={handleEditorKeyDown}
                                                onAttachmentClick={() => fileInputRef.current?.click()}
                                                attachedFilesCount={attachedFiles.length}
                                                showToolbar={true}
                                                clearTrigger={clearTrigger}
                                                className="flex-1"
                                            />
                                        </div>
                                        <Button
                                            onClick={handleSendMessage}
                                            disabled={!newMessage.trim() || isLoading}
                                            className="px-6 py-2 bg-[#1E4841] text-white hover:bg-[#1E4841]/90 disabled:opacity-50 self-end"
                                            aria-label="Send message"
                                        >
                                            {isLoading ? (
                                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                            ) : (
                                                <Send className="w-4 h-4" />
                                            )}
                                        </Button>
                                    </div>

                                    <p className="text-xs text-[#6B7280] mt-2">
                                        Press Ctrl+Enter to send • All messages are encrypted
                                    </p>
                                </div>
                            </div>
                        ) : (
                            <div className="flex-1 flex items-center justify-center h-[500px] lg:h-[600px] border rounded-lg bg-gray-50">
                                <div className="text-center max-w-md mx-auto p-6">
                                    <div className="w-16 h-16 bg-[#ECF4E9] rounded-full flex items-center justify-center mx-auto mb-4">
                                        <LockKeyhole className="w-8 h-8 text-[#1E4841]" />
                                    </div>
                                    <h3 className="text-lg font-medium text-[#111827] mb-2">Secure Messaging</h3>
                                    <p className="text-sm text-[#6B7280] mb-4">
                                        Select a conversation to start secure, end-to-end encrypted messaging with investigators and compliance teams.
                                    </p>
                                    <div className="flex items-center justify-center gap-2 text-xs text-[#1E4841]">
                                        <LockKeyhole className="w-4 h-4" />
                                        <span>All conversations are encrypted and secure</span>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </main>
        </div>
    );
}