"use client";

import Header from "@/components/dashboard-components/Header";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Users, UserCheck, UserX, Shield } from "lucide-react";
import { adminInvestigators } from "@/lib/mockData";

export default function UserManagementPage() {
    // Removed mock notification initialization - using real API data via NotificationContext

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="User Management"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">User Management</h1>
                        <p className="text-gray-600">Manage investigators, admins, and system users</p>
                    </div>

                    {/* User Statistics */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Users</CardTitle>
                                <Users className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">125</div>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Active Investigators</CardTitle>
                                <UserCheck className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">58</div>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Inactive Users</CardTitle>
                                <UserX className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">12</div>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Admins</CardTitle>
                                <Shield className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">8</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Investigators List */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-lg font-semibold text-gray-900">
                                    Investigators
                                </CardTitle>
                                <Button className="bg-[#BBF49C] text-[#1E4841] hover:bg-[#BBF49C]/90">
                                    Add New User
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {adminInvestigators.map((investigator, index) => (
                                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <div className="w-10 h-10 bg-[#BBF49C] rounded-full flex items-center justify-center">
                                                <span className="text-[#1E4841] font-medium text-sm">
                                                    {investigator.initials}
                                                </span>
                                            </div>
                                            <div>
                                                <h3 className="font-medium text-gray-900">{investigator.name}</h3>
                                                <p className="text-sm text-gray-600">Investigator</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <Badge 
                                                variant="secondary" 
                                                className={investigator.active ? 
                                                    "bg-green-100 text-green-800" : 
                                                    "bg-red-100 text-red-800"
                                                }
                                            >
                                                {investigator.active ? "Active" : "Inactive"}
                                            </Badge>
                                            <Button variant="outline" size="sm">
                                                Edit
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </main>
            </div>
        </>
    );
}