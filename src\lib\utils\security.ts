// Security utilities for input sanitization and validation
export const sanitizeForLog = (input: unknown): string => {
  if (typeof input === 'string') {
    return input.replace(/[\r\n\t]/g, ' ').replace(/[<>]/g, '');
  }
  if (typeof input === 'object' && input !== null) {
    return '[Object]';
  }
  return String(input).replace(/[\r\n\t]/g, ' ');
};

export const sanitizeHtml = (html: string): string => {
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
};

export const validateUserId = (userId: string): boolean => {
  return /^[a-zA-Z0-9_-]+$/.test(userId) && userId.length <= 50;
};

export const generateSecureRandom = (length: number): Buffer => {
  return require('crypto').randomBytes(length);
};