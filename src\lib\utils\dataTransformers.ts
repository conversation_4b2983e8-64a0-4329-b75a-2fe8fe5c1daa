/**
 * Data transformation utilities for converting API responses to frontend formats
 */

import { ReportData, RecentMessage, ActivityItem } from "@/lib/types";
import { ReportDocument } from "@/lib/db/models/interfaces";

/**
 * Get status color for reports
 */
export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'Draft':
      return 'bg-purple-100 text-purple-800';
    case 'New':
      return 'bg-blue-100 text-blue-800';
    case 'Under Review':
      return 'bg-yellow-100 text-yellow-800';
    case 'Awaiting Response':
      return 'bg-orange-100 text-orange-800';
    case 'Resolved':
      return 'bg-green-100 text-green-800';
    case 'Closed':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Get progress percentage based on status
 */
export const getProgressPercentage = (status: string): number => {
  switch (status) {
    case 'Draft':
      return 5;
    case 'New':
      return 10;
    case 'Under Review':
      return 50;
    case 'Awaiting Response':
      return 75;
    case 'Resolved':
      return 100;
    case 'Closed':
      return 100;
    default:
      return 0;
  }
};

/**
 * Get priority color for reports
 */
export const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'Low':
      return 'bg-gray-100 text-gray-800';
    case 'Medium':
      return 'bg-blue-100 text-blue-800';
    case 'High':
      return 'bg-orange-100 text-orange-800';
    case 'Critical':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Transform ReportDocument to ReportData for frontend display
 */
export const transformReportData = (report: ReportDocument): ReportData => {
  console.log('transformReportData: Input report:', report);

  const transformed = {
    id: report.reportId || report._id?.toString() || '',
    title: report.title || 'Untitled Report',
    status: report.status || 'New',
    statusColor: getStatusColor(report.status || 'New'),
    dateSubmitted: report.createdAt ? new Date(report.createdAt).toLocaleDateString() : '',
    lastUpdated: report.updatedAt ? new Date(report.updatedAt).toLocaleDateString() : '',
    priority: report.priority || 'Medium',
    category: report.category || 'General',
    progress: report.status || 'New',
    progressPercentage: getProgressPercentage(report.status || 'New')
  };

  console.log('transformReportData: Output transformed:', transformed);
  return transformed;
};

/**
 * Transform API conversation to RecentMessage format
 */
interface ConversationApiParticipant {
  _id: string;
  role: string;
  firstName?: string;
  lastName?: string;
}

interface ConversationApi {
  _id?: string;
  participants?: ConversationApiParticipant[];
  lastMessage?: { content?: string };
  lastMessageAt?: string | Date;
  hasUnreadMessages?: boolean;
  reportId?: { reportId?: string };
}

export const transformConversationToMessage = (
  conv: ConversationApi,
  index: number,
  currentUserId: string
): RecentMessage => {
  const investigator = conv.participants
    ?.filter((p) => p._id !== currentUserId && (p.role === 'investigator' || p.role === 'admin'))
    .map((p) => `${p.firstName ?? ''} ${p.lastName ?? ''}`.trim())
    .join(', ') || 'Investigator';

  const avatar = investigator.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase();

  return {
    id: conv.reportId?.reportId || `MSG-${index + 1}`,
    conversationId: conv._id,
    investigator,
    message: conv.lastMessage?.content || "Click to view secure conversation",
    time: conv.lastMessageAt
      ? new Date(conv.lastMessageAt).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })
      : 'Now',
    avatar,
    isUnread: conv.hasUnreadMessages || false
  };
};

/**
 * Transform API activity to ActivityItem format
 */
interface ActivityApi {
  _id?: string;
  id?: string;
  type?: string;
  title?: string;
  description?: string;
  createdAt?: string | Date;
  reportId?: string;
}

export const transformActivityData = (activity: ActivityApi): ActivityItem => {
  const activityType = activity.type || 'status_update';

  // Map activity type to icon
  const getIcon = (type: string): 'message' | 'status' | 'check' => {
    switch (type) {
      case 'message':
        return 'message';
      case 'resolution':
        return 'check';
      case 'status_update':
      default:
        return 'status';
    }
  };

  return {
    id: activity._id || activity.id || '',
    type: activityType as 'message' | 'status_update' | 'resolution',
    title: activity.title || 'Activity Update',
    description: activity.description || '',
    time: activity.createdAt
      ? new Date(activity.createdAt).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })
      : 'Now',
    reportId: activity.reportId || '',
    icon: getIcon(activityType)
  };
};

/**
 * Format date for display
 */
export const formatDate = (date: Date | string, format: 'short' | 'long' = 'short'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (format === 'long') {
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    });
  }
  
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Format time for display
 */
export const formatTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit', 
    hour12: true 
  });
};

/**
 * Get relative time (e.g., "2 hours ago")
 */
export const getRelativeTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInMs = now.getTime() - dateObj.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  
  return formatDate(dateObj);
};

/**
 * Generate avatar initials from name
 */
export const getAvatarInitials = (name: string): string => {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .slice(0, 2)
    .toUpperCase();
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};
