import connectDB from './mongodb';
import { DataService } from './dataService';
import { Company, User, Report, Conversation, Message, Notification } from './models';
import mongoose from 'mongoose';

// Clear all existing data
export async function clearDatabase() {
  try {
    await connectDB();
    console.log('🗑️  Clearing existing data...');
    
    // Clear all collections
    await Promise.all([
      Company.deleteMany({}),
      User.deleteMany({}),
      Report.deleteMany({}),
      Conversation.deleteMany({}),
      Message.deleteMany({}),
      Notification.deleteMany({})
    ]);
    
    console.log('✅ Database cleared successfully');
  } catch (error) {
    console.error('❌ Error clearing database:', error);
    throw error;
  }
}

// Generate unique report ID
function generateReportId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `WB-${timestamp}-${random}`.toUpperCase();
}

// Refined seed data following the workflow
export async function seedRefinedData() {
  try {
    await connectDB();
    console.log('🌱 Starting refined database seeding...');

    // Step 1: Create Companies
    console.log('📊 Creating companies...');
    const companies = await Promise.all([
      Company.create({
        name: 'TechCorp Industries',
        industry: 'Technology',
        size: 'Large',
        website: 'https://techcorp.com',
        address: {
          street: '123 Tech Street',
          city: 'San Francisco',
          state: 'CA',
          zipCode: '94105',
          country: 'USA'
        },
        contactEmail: '<EMAIL>',
        contactPhone: '******-0123',
        subscriptionStatus: 'Active',
        isActive: true
      }),
      Company.create({
        name: 'Global Manufacturing Ltd',
        industry: 'Manufacturing',
        size: 'Enterprise',
        website: 'https://globalmanufacturing.com',
        address: {
          street: '456 Industrial Ave',
          city: 'Detroit',
          state: 'MI',
          zipCode: '48201',
          country: 'USA'
        },
        contactEmail: '<EMAIL>',
        contactPhone: '******-0456',
        subscriptionStatus: 'Active',
        isActive: true
      })
    ]);

    console.log(`✅ Created ${companies.length} companies`);

    // Step 2: Create Users with proper company associations
    console.log('👥 Creating users...');
    
    // Create admins/investigators for each company
    const adminUsers = await Promise.all([
      DataService.createUser({
        email: '<EMAIL>',
        firstName: 'Sarah',
        lastName: 'Johnson',
        password: 'admin123',
        role: 'admin',
        companyId: companies[0]._id.toString(),
        isActive: true
      }),
      DataService.createUser({
        email: '<EMAIL>',
        firstName: 'Michael',
        lastName: 'Chen',
        password: 'investigator123',
        role: 'investigator',
        companyId: companies[0]._id.toString(),
        isActive: true
      }),
      DataService.createUser({
        email: '<EMAIL>',
        firstName: 'Emily',
        lastName: 'Rodriguez',
        password: 'admin123',
        role: 'admin',
        companyId: companies[1]._id.toString(),
        isActive: true
      }),
      DataService.createUser({
        email: '<EMAIL>',
        firstName: 'David',
        lastName: 'Thompson',
        password: 'investigator123',
        role: 'investigator',
        companyId: companies[1]._id.toString(),
        isActive: true
      })
    ]);

    // Create whistleblowers for each company
    const whistleblowerUsers = await Promise.all([
      DataService.createUser({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'employee123',
        role: 'whistleblower',
        companyId: companies[0]._id.toString(),
        isActive: true
      }),
      DataService.createUser({
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        password: 'whistleblower123',
        role: 'whistleblower',
        companyId: companies[0]._id.toString(),
        isActive: true
      }),
      DataService.createUser({
        email: '<EMAIL>',
        firstName: 'Robert',
        lastName: 'Wilson',
        password: 'whistleblower123',
        role: 'whistleblower',
        companyId: companies[1]._id.toString(),
        isActive: true
      }),
      DataService.createUser({
        email: '<EMAIL>',
        firstName: 'Lisa',
        lastName: 'Brown',
        password: 'whistleblower123',
        role: 'whistleblower',
        companyId: companies[1]._id.toString(),
        isActive: true
      })
    ]);

    console.log(`✅ Created ${adminUsers.length} admin/investigator users`);
    console.log(`✅ Created ${whistleblowerUsers.length} whistleblower users`);

    // Step 3: Create Reports following the workflow
    console.log('📋 Creating reports...');
    
    const reportData = [
      {
        whistleblower: whistleblowerUsers[0], // John Doe from TechCorp
        admin: adminUsers[0], // Sarah Johnson (admin)
        investigator: adminUsers[1], // Michael Chen (investigator)
        company: companies[0],
        report: {
          title: 'Unauthorized Access to Customer Data',
          description: 'I have observed that several employees in the IT department are accessing customer personal information without proper authorization. This includes viewing credit card details and personal addresses for customers they are not assigned to support. I have screenshots of the unauthorized access logs.',
          category: 'Other',
          priority: 'High',
          incidentDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
          location: 'IT Department, 3rd Floor'
        }
      },
      {
        whistleblower: whistleblowerUsers[1], // Jane Smith from TechCorp
        admin: adminUsers[0], // Sarah Johnson (admin)
        investigator: adminUsers[1], // Michael Chen (investigator)
        company: companies[0],
        report: {
          title: 'Financial Irregularities in Expense Reports',
          description: 'I have noticed that my manager has been submitting false expense reports for business trips that never occurred. The amounts are significant, ranging from $2,000 to $5,000 per fake trip. I have copies of the expense reports and can verify that these trips did not happen.',
          category: 'Financial Misconduct',
          priority: 'Critical',
          incidentDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
          location: 'Finance Department'
        }
      },
      {
        whistleblower: whistleblowerUsers[2], // Robert Wilson from Global Manufacturing
        admin: adminUsers[2], // Emily Rodriguez (admin)
        investigator: adminUsers[3], // David Thompson (investigator)
        company: companies[1],
        report: {
          title: 'Safety Equipment Not Provided to Workers',
          description: 'Workers on the factory floor are not being provided with proper safety equipment including helmets, safety glasses, and protective gloves. Management is aware but claims it is too expensive. Several minor injuries have already occurred.',
          category: 'Safety Violation',
          priority: 'Critical',
          incidentDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          location: 'Factory Floor A'
        }
      },
      {
        whistleblower: whistleblowerUsers[3], // Lisa Brown from Global Manufacturing
        admin: adminUsers[2], // Emily Rodriguez (admin)
        investigator: adminUsers[3], // David Thompson (investigator)
        company: companies[1],
        report: {
          title: 'Discrimination in Hiring Practices',
          description: 'I have witnessed discriminatory hiring practices where qualified candidates are being rejected based on their age and gender. HR manager has made several inappropriate comments during hiring meetings. I can provide specific examples and dates.',
          category: 'Discrimination',
          priority: 'High',
          incidentDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000), // 21 days ago
          location: 'HR Department'
        }
      }
    ];

    const createdReports = [];
    const createdConversations = [];
    const createdMessages = [];
    const createdNotifications = [];

    for (const data of reportData) {
      // Create report
      const report = await DataService.createReport({
        reportId: generateReportId(),
        userId: new mongoose.Types.ObjectId((data.whistleblower as unknown as { _id: string })._id),
        companyId: new mongoose.Types.ObjectId((data.company as unknown as { _id: string })._id),
        title: data.report.title,
        description: data.report.description,
        category: data.report.category,
        priority: data.report.priority,
        status: 'New',
        isAnonymous: false,
        incidentDate: data.report.incidentDate,
        location: data.report.location,
        assignedInvestigator: new mongoose.Types.ObjectId((data.investigator as unknown as { _id: string })._id)
      });

      createdReports.push(report);
      console.log(`📋 Created report: ${report.title}`);

      // Create conversation for the report (1-on-1: whistleblower and investigator)
      const conversation = await DataService.createConversation({
        reportId: (report as unknown as { _id: { toString: () => string } })._id.toString(),
        participants: [(data.whistleblower as unknown as { _id: { toString: () => string } })._id.toString(), (data.investigator as unknown as { _id: { toString: () => string } })._id.toString()]
      });

      createdConversations.push(conversation);
      console.log(`💬 Created conversation for report: ${report.reportId}`);

      // Create admin confirmation message
      const adminMessage = await DataService.createMessage({
        conversationId: (conversation as unknown as { _id: { toString: () => string } })._id.toString(),
        senderId: (data.admin as unknown as { _id: { toString: () => string } })._id.toString(),
        content: `Dear ${(data.whistleblower as unknown as { firstName: string }).firstName},

Thank you for submitting your report "${(report as unknown as { title: string }).title}" (ID: ${(report as unknown as { reportId: string }).reportId}). We have received your submission and take all reports seriously.

Your report has been assigned to ${(data.investigator as unknown as { firstName: string }).firstName} ${(data.investigator as unknown as { lastName: string }).lastName} for investigation. We will keep you updated on our progress and may reach out if we need additional information.

All communications regarding this matter will remain confidential. You can expect an initial update within 48 hours.

Best regards,
${(data.admin as unknown as { firstName: string }).firstName} ${(data.admin as unknown as { lastName: string }).lastName}
Compliance Team`,
        messageType: 'text'
      });

      createdMessages.push(adminMessage);
      console.log(`📨 Created admin confirmation message`);

      // Create investigator acknowledgment message (sent a bit later)
      await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay for different timestamps
      
      const investigatorMessage = await DataService.createMessage({
        conversationId: (conversation as unknown as { _id: { toString: () => string } })._id.toString(),
        senderId: (data.investigator as unknown as { _id: { toString: () => string } })._id.toString(),
        content: `Hello ${(data.whistleblower as unknown as { firstName: string }).firstName},

I am ${(data.investigator as unknown as { firstName: string }).firstName} ${(data.investigator as unknown as { lastName: string }).lastName}, the investigator assigned to your case. I have reviewed your initial report and will begin the investigation process immediately.

Based on the nature of your report, I may need to:
- Review relevant documentation and records
- Interview relevant personnel (maintaining confidentiality)
- Examine any evidence you have provided

I will provide you with regular updates on the investigation progress. Please feel free to reach out if you have any additional information or concerns.

Thank you for bringing this matter to our attention.

Best regards,
${(data.investigator as unknown as { firstName: string }).firstName} ${(data.investigator as unknown as { lastName: string }).lastName}
Lead Investigator`,
        messageType: 'text'
      });

      createdMessages.push(investigatorMessage);
      console.log(`🔍 Created investigator acknowledgment message`);

      // Create notifications for the whistleblower
      const reportNotification = await Notification.create({
        userId: (data.whistleblower as unknown as { _id: string })._id,
        type: 'report_update',
        title: 'Report Received',
        message: `Your report "${(report as unknown as { title: string }).title}" has been received and assigned to an investigator.`,
        priority: 'medium',
        reportId: (report as unknown as { _id: string })._id,
        actionUrl: `/dashboard/whistleblower/my-reports`,
        status: 'unread'
      });

      const messageNotification = await Notification.create({
        userId: (data.whistleblower as unknown as { _id: string })._id,
        type: 'message',
        title: 'New Message',
        message: `You have received a new message regarding your report "${(report as unknown as { title: string }).title}".`,
        priority: 'medium',
        reportId: (report as unknown as { _id: string })._id,
        actionUrl: `/dashboard/whistleblower/secure-message/${(conversation as unknown as { _id: string })._id}`,
        status: 'unread'
      });

      createdNotifications.push(reportNotification, messageNotification);

      // Create notifications for admin and investigator
      const adminNotification = await Notification.create({
        userId: (data.admin as unknown as { _id: string })._id,
        type: 'report_update',
        title: 'New Report Submitted',
        message: `A new ${data.report.priority.toLowerCase()} priority report has been submitted: "${(report as unknown as { title: string }).title}".`,
        priority: data.report.priority === 'Critical' ? 'urgent' : 'high',
        reportId: (report as unknown as { _id: string })._id,
        actionUrl: `/dashboard/admin/case-management`,
        status: 'unread'
      });

      const investigatorNotification = await Notification.create({
        userId: (data.investigator as unknown as { _id: string })._id,
        type: 'report_update',
        title: 'Case Assigned',
        message: `You have been assigned to investigate: "${(report as unknown as { title: string }).title}".`,
        priority: 'high',
        reportId: (report as unknown as { _id: string })._id,
        actionUrl: `/dashboard/admin/case-management`,
        status: 'unread'
      });

      createdNotifications.push(adminNotification, investigatorNotification);

      console.log(`🔔 Created notifications for all participants`);

      // Update report status to "Under Review" after admin/investigator messages
      await Report.findByIdAndUpdate((report as unknown as { _id: string })._id, { 
        status: 'Under Review',
        progress: 15
      });

      console.log(`📊 Updated report status to "Under Review"`);
    }

    // Create some follow-up messages to make conversations more realistic
    console.log('💬 Creating follow-up messages...');
    
    // Add a follow-up from the first whistleblower
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const followUpMessage = await DataService.createMessage({
      conversationId: (createdConversations[0] as unknown as { _id: { toString: () => string } })._id.toString(),
      senderId: (whistleblowerUsers[0] as unknown as { _id: { toString: () => string } })._id.toString(),
      content: `Thank you for the quick response. I have additional screenshots that show the unauthorized access patterns. I noticed this has been happening for at least 3 months based on the log timestamps. Should I send these screenshots through this secure channel?`,
      messageType: 'text'
    });

    createdMessages.push(followUpMessage);

    // Investigator response
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const investigatorResponse = await DataService.createMessage({
      conversationId: (createdConversations[0] as unknown as { _id: { toString: () => string } })._id.toString(),
      senderId: (adminUsers[1] as unknown as { _id: { toString: () => string } })._id.toString(), // Michael Chen (investigator)
      content: `Yes, please share the screenshots through this secure channel. The additional evidence will be very helpful for our investigation. I'm also arranging to review the complete access logs for the past 6 months to identify the full scope of unauthorized access.`,
      messageType: 'text'
    });

    createdMessages.push(investigatorResponse);

    console.log(`✅ Created ${createdReports.length} reports`);
    console.log(`✅ Created ${createdConversations.length} conversations`);
    console.log(`✅ Created ${createdMessages.length} messages`);
    console.log(`✅ Created ${createdNotifications.length} notifications`);

    // Final summary
    const summary = {
      companies: companies.length,
      adminUsers: adminUsers.length,
      whistleblowerUsers: whistleblowerUsers.length,
      reports: createdReports.length,
      conversations: createdConversations.length,
      messages: createdMessages.length,
      notifications: createdNotifications.length
    };

    console.log('🎉 Refined database seeding completed successfully!');
    console.log('📊 Summary:', summary);

    return {
      success: true,
      message: 'Database seeded with consistent, workflow-based data',
      data: summary
    };

  } catch (error) {
    console.error('❌ Error in refined seed function:', error);
    throw error;
  }
}

// Main seeding function that clears and reseeds
export async function reseedDatabase() {
  try {
    await clearDatabase();
    return await seedRefinedData();
  } catch (error) {
    console.error('❌ Error reseeding database:', error);
    throw error;
  }
}


