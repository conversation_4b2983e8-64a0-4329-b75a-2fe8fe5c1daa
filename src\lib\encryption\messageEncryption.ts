import crypto from 'crypto';
import { generateSecureRandom } from '@/lib/utils/security';

const ENCRYPTION_ALGORITHM = 'aes-256-cbc';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits

// Get encryption key from environment or generate a consistent default for development
const ENCRYPTION_KEY = process.env.MESSAGE_ENCRYPTION_KEY 
  ? Buffer.from(process.env.MESSAGE_ENCRYPTION_KEY, 'hex')
  : crypto.scryptSync('whistleblower-message-encryption-key', 'salt', KEY_LENGTH);

export interface EncryptedMessage {
  encryptedContent: string;
  iv: string;
  tag: string;
  isEncrypted: boolean;
}

export interface DecryptedMessage {
  content: string;
  isEncrypted: boolean;
}

/**
 * Encrypt a message using AES-256-CBC
 */
export function encryptMessage(plaintext: string): EncryptedMessage {
  try {
    // Generate a cryptographically secure random initialization vector
    const iv = generateSecureRandom(IV_LENGTH);
    
    // Create cipher with CBC mode using the modern API
    const cipher = crypto.createCipheriv(ENCRYPTION_ALGORITHM, ENCRYPTION_KEY, iv);
    
    // Encrypt the message
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Generate a simple tag for integrity check
    const tag = crypto.createHash('sha256').update(encrypted + iv.toString('hex')).digest('hex').substring(0, 32);
    
    return {
      encryptedContent: encrypted,
      iv: iv.toString('hex'),
      tag: tag,
      isEncrypted: true
    };
  } catch (error) {
    console.error('Message encryption error:', error);
    // Fallback: return unencrypted message with flag
    return {
      encryptedContent: plaintext,
      iv: '',
      tag: '',
      isEncrypted: false
    };
  }
}

/**
 * Decrypt a message using AES-256-CBC
 */
export function decryptMessage(encryptedData: EncryptedMessage): DecryptedMessage {
  try {
    // If message is not encrypted, return as-is
    if (!encryptedData.isEncrypted || !encryptedData.iv || !encryptedData.tag) {
      return {
        content: encryptedData.encryptedContent,
        isEncrypted: false
      };
    }
    
    // Validate input data
    if (!encryptedData.encryptedContent || !encryptedData.iv) {
      throw new Error('Invalid encrypted data format');
    }
    
    // Verify tag for basic integrity check
    const expectedTag = crypto.createHash('sha256').update(encryptedData.encryptedContent + encryptedData.iv).digest('hex').substring(0, 32);
    if (expectedTag !== encryptedData.tag) {
      throw new Error('Message integrity check failed');
    }
    
    // Convert IV back to buffer with validation
    if (encryptedData.iv.length !== IV_LENGTH * 2) {
      throw new Error('Invalid IV length');
    }
    const iv = Buffer.from(encryptedData.iv, 'hex');
    
    // Create decipher with CBC mode using the modern API
    const decipher = crypto.createDecipheriv(ENCRYPTION_ALGORITHM, ENCRYPTION_KEY, iv);
    
    // Decrypt the message
    let decrypted = decipher.update(encryptedData.encryptedContent, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return {
      content: decrypted,
      isEncrypted: true
    };
  } catch (error) {
    console.error('Message decryption error:', error);
    // Fallback: return encrypted content with warning
    return {
      content: '[Encrypted message - decryption failed]',
      isEncrypted: true
    };
  }
}

/**
 * Encrypt HTML content for rich text messages
 */
export function encryptHtmlContent(htmlContent: string): EncryptedMessage {
  return encryptMessage(htmlContent);
}

/**
 * Decrypt HTML content for rich text messages
 */
export function decryptHtmlContent(encryptedData: EncryptedMessage): DecryptedMessage {
  return decryptMessage(encryptedData);
}

/**
 * Generate a new encryption key (for key rotation)
 */
export function generateEncryptionKey(): string {
  return generateSecureRandom(KEY_LENGTH).toString('hex');
}

/**
 * Validate encryption key format
 */
export function validateEncryptionKey(key: string): boolean {
  try {
    const keyBuffer = Buffer.from(key, 'hex');
    return keyBuffer.length === KEY_LENGTH;
  } catch {
    return false;
  }
}