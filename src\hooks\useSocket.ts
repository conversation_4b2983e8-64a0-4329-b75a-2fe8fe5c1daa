import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { Message, Conversation } from '@/lib/types';
import { connectionMonitor } from '@/lib/socket/connectionMonitor';
import { connectionThrottle } from '@/lib/socket/connectionThrottle';
import { NotificationEvents } from '@/lib/utils/notificationEvents';

export interface MessageWithSender extends Omit<Message, 'senderId'> {
  senderId: {
    _id: string;
    firstName?: string;
    lastName?: string;
    role: string;
  };
}

export interface TypingUser {
  userId: string;
  userName: string;
  conversationId: string;
}

export interface UserStatus {
  userId: string;
  isOnline: boolean;
  timestamp: Date;
}

export interface UseSocketReturn {
  isConnected: boolean;
  onlineUsers: Set<string>;
  typingUsers: Map<string, Set<string>>; // conversationId -> Set of userIds
  connectionHealth: {
    isStable: boolean;
    reconnectionCount: number;
    lastConnectedAt?: Date;
    getHealthReport: () => { status: 'healthy' | 'unstable' | 'critical'; issues: string[] };
  };
  sendMessage: (messageData: {
    conversationId: string;
    content: string;
    htmlContent?: string;
    messageType?: 'text' | 'file' | 'system';
    attachments?: Array<{
      fileName: string;
      fileUrl: string;
      fileSize: number;
      mimeType: string;
    }>;
  }) => Promise<void>;
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;
  markMessageAsRead: (messageId: string, conversationId: string) => Promise<void>;
  createConversation: (reportId: string, participants: string[]) => Promise<void>;
  refreshMessages: (conversationId: string) => Promise<MessageWithSender[]>;
}

export const useSocket = (
  userId: string | null,
  userRole: string | null,
  onNewMessage?: (message: MessageWithSender) => void,
  onUserTyping?: (data: { conversationId: string; userId: string; isTyping: boolean }) => void,
  onUserStatusChange?: (status: UserStatus) => void,
  onConversationCreated?: (conversation: Conversation) => void
): UseSocketReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const [typingUsers, setTypingUsers] = useState<Map<string, Set<string>>>(new Map());
  const [retryTrigger, setRetryTrigger] = useState(0);
  
  const socketRef = useRef<Socket | null>(null);
  const typingTimeoutRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Store callback refs to prevent reconnections
  const callbacksRef = useRef({ onNewMessage, onUserTyping, onUserStatusChange });
  
  // Update callback refs when they change, but only if they're actually different
  useEffect(() => {
    const callbacks = { onNewMessage, onUserTyping, onUserStatusChange };
    if (JSON.stringify(callbacksRef.current) !== JSON.stringify(callbacks)) {
      callbacksRef.current = callbacks;
    }
  }, [onNewMessage, onUserTyping, onUserStatusChange]);

  // Initialize socket connection
  useEffect(() => {
    if (!userId) return;

    // Check if connection is throttled
    if (!connectionThrottle.canConnect()) {
      console.warn('⏳ Connection throttled, waiting for backoff period');
      const backoffDelay = connectionThrottle.getBackoffDelay();
      
      const throttleTimeout = setTimeout(() => {
        // Retry after backoff period
        if (socketRef.current?.connected) return; // Don't reconnect if already connected
        console.log('🔄 Retrying connection after throttle period');
        // Trigger retry by incrementing the retry counter
        setRetryTrigger(prev => prev + 1);
      }, backoffDelay);

      return () => clearTimeout(throttleTimeout);
    }

    console.log(`Initializing socket for user ${userId} with role ${userRole}`);
    connectionThrottle.recordAttempt();

    const socket = io(process.env.NODE_ENV === 'production' 
      ? process.env.NEXTAUTH_URL || '' 
      : 'http://localhost:3002', {
      transports: ['websocket'], // Use only WebSocket transport
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 30000,
      timeout: 20000,
      forceNew: true,
      upgrade: false // Disable transport upgrade
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on('connect', () => {
      console.log('Socket connected:', socket.id);
      connectionMonitor.onConnect(socket);
      connectionThrottle.resetBackoff(); // Reset backoff on successful connection
      setIsConnected(true);
      
      // Authenticate user
      socket.emit('authenticate', {
        userId,
        userRole
      });
    });

    socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      connectionMonitor.onDisconnect(reason);
      setIsConnected(false);
      
      // Increase backoff if it's an unexpected disconnection
      if (reason !== 'io client disconnect' && reason !== 'transport close') {
        connectionThrottle.increaseBackoff();
      }
      
      // Clear online users and typing indicators on disconnect
      setOnlineUsers(new Set());
      setTypingUsers(new Map());
    });

    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      connectionMonitor.onDisconnect(`Connection error: ${error.message || error}`);
      connectionThrottle.increaseBackoff(); // Increase backoff on connection errors
      setIsConnected(false);
    });

    // Authentication success/error handlers
    socket.on('authenticated', (data) => {
      console.log('Authentication successful:', data);
      connectionMonitor.onAuthSuccess(userId);
    });

    socket.on('auth_error', (error) => {
      console.error('Authentication error:', error);
      connectionMonitor.onAuthError(error);
      setIsConnected(false);
    });

    // Message event handlers
    socket.on('new_message', (message: MessageWithSender) => {
      console.log('New message received:', message);
      callbacksRef.current.onNewMessage?.(message);
    });

    // Typing event handlers
    socket.on('user_typing', (data: { conversationId: string; userId: string; isTyping: boolean }) => {
      setTypingUsers(prev => {
        const newMap = new Map(prev);
        const conversationTyping = newMap.get(data.conversationId) || new Set();
        
        if (data.isTyping) {
          conversationTyping.add(data.userId);
        } else {
          conversationTyping.delete(data.userId);
        }
        
        if (conversationTyping.size > 0) {
          newMap.set(data.conversationId, conversationTyping);
        } else {
          newMap.delete(data.conversationId);
        }
        
        return newMap;
      });
      
      callbacksRef.current.onUserTyping?.(data);
    });

    // User status event handlers
    socket.on('user_status_change', (status: UserStatus) => {
      setOnlineUsers(prev => {
        const newSet = new Set(prev);
        if (status.isOnline) {
          newSet.add(status.userId);
        } else {
          newSet.delete(status.userId);
        }
        return newSet;
      });
      
      callbacksRef.current.onUserStatusChange?.(status);
    });

    // Message read receipt handlers
    socket.on('message_read', (data: { messageId: string; userId: string; readAt: Date }) => {
      console.log('Message read receipt:', data);
      // Handle read receipt updates in the UI
    });

    // Error handlers
    socket.on('message_error', (error) => {
      console.error('Message error:', error);
    });

    return () => {
      console.log(`Cleaning up socket for user ${userId}`);
      socket.disconnect();
      socketRef.current = null;
    };
  }, [userId, userRole, retryTrigger]); // Added retryTrigger for throttled reconnections

  // Stop typing function ref
  const stopTypingRef = useRef<(conversationId: string) => void>(() => {});

  // Send message
  const sendMessage = useCallback(async (messageData: {
    conversationId: string;
    content: string;
    htmlContent?: string;
    messageType?: 'text' | 'file' | 'system';
    attachments?: Array<{
      fileName: string;
      fileUrl: string;
      fileSize: number;
      mimeType: string;
    }>;
  }) => {
    if (!socketRef.current || !userId) return;

    try {
      // Save to database via API - this will handle real-time broadcasting automatically
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...messageData,
          senderId: userId,
          messageType: messageData.messageType || 'text'
        })
      });

      const data = await response.json();
      if (data.success) {
        // Trigger notification event for real-time updates
        NotificationEvents.messageSent(
          messageData.conversationId,
          messageData.content
        );

        // Stop typing indicator
        stopTypingRef.current?.(messageData.conversationId);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }, [userId]);

  // Join conversation
  const joinConversation = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('join_conversation', conversationId);
      console.log('Joined conversation:', conversationId);
    }
  }, []);

  // Leave conversation
  const leaveConversation = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('leave_conversation', conversationId);
      console.log('Left conversation:', conversationId);
    }
  }, []);

  // Start typing
  const startTyping = useCallback((conversationId: string) => {
    if (!socketRef.current || !userId) return;
    
    socketRef.current.emit('typing_start', {
      conversationId,
      userId
    });
    
    // Clear existing timeout for this conversation
    const existingTimeout = typingTimeoutRef.current.get(conversationId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }
    
    // Set new timeout to stop typing after 3 seconds of inactivity
    const timeout = setTimeout(() => {
      stopTypingRef.current?.(conversationId);
    }, 3000);
    
    typingTimeoutRef.current.set(conversationId, timeout);
  }, [userId]);

  // Stop typing
  const stopTyping = useCallback((conversationId: string) => {
    if (!socketRef.current || !userId) return;
    
    socketRef.current.emit('typing_stop', {
      conversationId,
      userId
    });
    
    // Clear timeout
    const timeout = typingTimeoutRef.current.get(conversationId);
    if (timeout) {
      clearTimeout(timeout);
      typingTimeoutRef.current.delete(conversationId);
    }
  }, [userId]);

  // Assign stopTyping to ref
  stopTypingRef.current = stopTyping;

  // Mark message as read
  const markMessageAsRead = useCallback(async (messageId: string, conversationId: string) => {
    if (!userId) return;
    
    try {
      await fetch(`/api/messages?messageId=${messageId}&action=mark_read`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });

      // Emit read receipt via socket
      if (socketRef.current) {
        socketRef.current.emit('mark_message_read', {
          messageId,
          conversationId,
          userId
        });
      }
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }, [userId]);

  // Create conversation
  const createConversation = useCallback(async (reportId: string, participants: string[]) => {
    try {
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reportId, participants })
      });

      const data = await response.json();
      if (data.success) {
        onConversationCreated?.(data.data);
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  }, [onConversationCreated]);

  // Refresh messages for a conversation
  const refreshMessages = useCallback(async (conversationId: string): Promise<MessageWithSender[]> => {
    try {
      const response = await fetch(`/api/messages?conversationId=${conversationId}`);
      const data = await response.json();
      
      if (data.success) {
        return data.data as MessageWithSender[];
      }
      
      return [];
    } catch (error) {
      console.error('Error refreshing messages:', error);
      return [];
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    const currentTimeouts = typingTimeoutRef.current;
    return () => {
      // Clear all typing timeouts
      currentTimeouts.forEach(timeout => clearTimeout(timeout));
      currentTimeouts.clear();
    };
  }, []);

  return {
    isConnected,
    onlineUsers,
    typingUsers,
    connectionHealth: {
      isStable: connectionMonitor.getStats().isStable,
      reconnectionCount: connectionMonitor.getStats().totalReconnections,
      lastConnectedAt: connectionMonitor.getStats().lastConnectedAt,
      getHealthReport: () => connectionMonitor.getHealthReport()
    },
    sendMessage,
    joinConversation,
    leaveConversation,
    startTyping,
    stopTyping,
    markMessageAsRead,
    createConversation,
    refreshMessages
  };
};