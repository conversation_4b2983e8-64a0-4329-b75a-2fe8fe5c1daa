import { useEffect, useRef, useState, useCallback } from 'react';
import { Message, Conversation } from '@/lib/types';
import { apiClient } from '@/lib/api/client';

export interface MessageWithSender extends Omit<Message, 'senderId'> {
  senderId: {
    _id: string;
    firstName?: string;
    lastName?: string;
    role: string;
  };
}

export interface TypingUser {
  userId: string;
  userName: string;
  conversationId: string;
}

export interface UserStatus {
  userId: string;
  isOnline: boolean;
  timestamp: Date;
}

export interface UseMessagingReturn {
  isConnected: boolean;
  onlineUsers: Set<string>;
  typingUsers: Map<string, Set<string>>; // conversationId -> Set of userIds
  sendMessage: (messageData: {
    conversationId: string;
    content: string;
    htmlContent?: string;
    messageType?: 'text' | 'file' | 'system';
    attachments?: Array<{
      fileName: string;
      fileUrl: string;
      fileSize: number;
      mimeType: string;
    }>;
  }) => Promise<void>;
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;
  markMessageAsRead: (messageId: string) => Promise<void>;
  createConversation: (reportId: string, participants: string[]) => Promise<void>;
  refreshMessages: (conversationId: string) => Promise<MessageWithSender[]>;
}

export const useMessaging = (
  userId: string | null,
  userRole: string | null,
  onNewMessage?: (message: MessageWithSender) => void,
  onUserTyping?: (data: { conversationId: string; userId: string; isTyping: boolean }) => void,
  onUserStatusChange?: (status: UserStatus) => void,
  onConversationCreated?: (conversation: Conversation) => void
): UseMessagingReturn => {
  const [isConnected] = useState(true); // Always connected for polling
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const [typingUsers, setTypingUsers] = useState<Map<string, Set<string>>>(new Map());
  const [activeConversations, setActiveConversations] = useState<Set<string>>(new Set());
  
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const typingTimeoutRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const lastMessageTimestamps = useRef<Map<string, Date>>(new Map());

  // Polling for new messages
  const pollMessages = useCallback(async () => {
    if (!userId || activeConversations.size === 0) return;

    try {
      for (const conversationId of activeConversations) {
        const lastTimestamp = lastMessageTimestamps.current.get(conversationId);
        const timestampParam = lastTimestamp ? `&since=${lastTimestamp.toISOString()}` : '';
        
        const data = await apiClient.get(`/api/messages?conversationId=${conversationId}${timestampParam}`) as { success: boolean; data: MessageWithSender[] };
        
        if (data.success && data.data.length > 0) {
          const newMessages = data.data.filter(message => 
            message.senderId._id !== userId // Only process messages from others
          );
          
          if (newMessages.length > 0) {
            // Update last timestamp with the latest message from others
            const latestMessage = newMessages[newMessages.length - 1];
            if (latestMessage.createdAt) {
              lastMessageTimestamps.current.set(conversationId, new Date(latestMessage.createdAt));
            }
            
            // Notify about new messages
            newMessages.forEach(message => {
              onNewMessage?.(message);
            });
          }
        }
      }
    } catch (error) {
      console.error('Error polling messages:', error);
    }
  }, [userId, activeConversations, onNewMessage]);

  // Start polling when component mounts
  useEffect(() => {
    if (userId && activeConversations.size > 0) {
      pollingIntervalRef.current = setInterval(pollMessages, 2000); // Poll every 2 seconds
      
      return () => {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }
      };
    }
  }, [userId, activeConversations.size, pollMessages]);

  // Simulate online users (in a real app, this would come from the server)
  useEffect(() => {
    const simulateOnlineUsers = () => {
      // This is a mock - in reality, you'd get this from your backend
      setOnlineUsers(new Set(['user1', 'user2', 'admin1']));
    };
    
    simulateOnlineUsers();
    const interval = setInterval(simulateOnlineUsers, 30000); // Update every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Stop typing function ref
  const stopTypingRef = useRef<(conversationId: string) => void>(() => {});

  // Send message
  const sendMessage = useCallback(async (messageData: {
    conversationId: string;
    content: string;
    htmlContent?: string;
    messageType?: 'text' | 'file' | 'system';
    attachments?: Array<{
      fileName: string;
      fileUrl: string;
      fileSize: number;
      mimeType: string;
    }>;
  }) => {
    if (!userId) return;

    try {
      console.log('useMessaging: Sending message with auth token:', !!localStorage.getItem('auth_token'));
      const data = await apiClient.post('/api/messages', {
        ...messageData,
        senderId: userId,
        messageType: messageData.messageType || 'text'
      }) as { success: boolean };
      if (data.success) {
        // Stop typing indicator
        stopTypingRef.current?.(messageData.conversationId);
        
        // Update last message timestamp to current time to avoid fetching our own message
        lastMessageTimestamps.current.set(messageData.conversationId, new Date());
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }, [userId]);

  // Join conversation
  const joinConversation = useCallback((conversationId: string) => {
    setActiveConversations(prev => new Set([...prev, conversationId]));
    
    // Set initial timestamp for polling
    lastMessageTimestamps.current.set(conversationId, new Date());
  }, []);

  // Leave conversation
  const leaveConversation = useCallback((conversationId: string) => {
    setActiveConversations(prev => {
      const newSet = new Set(prev);
      newSet.delete(conversationId);
      return newSet;
    });
    
    // Clean up timestamps and typing indicators
    lastMessageTimestamps.current.delete(conversationId);
    setTypingUsers(prev => {
      const newMap = new Map(prev);
      newMap.delete(conversationId);
      return newMap;
    });
  }, []);

  // Start typing
  const startTyping = useCallback((conversationId: string) => {
    if (!userId) return;
    
    // In a real implementation, you'd send this to the server
    // For now, we'll just manage local state
    
    // Clear existing timeout for this conversation
    const existingTimeout = typingTimeoutRef.current.get(conversationId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }
    
    // Set new timeout to stop typing after 3 seconds of inactivity
    const timeout = setTimeout(() => {
      stopTypingRef.current?.(conversationId);
    }, 3000);
    
    typingTimeoutRef.current.set(conversationId, timeout);
  }, [userId]);

  // Stop typing
  const stopTyping = useCallback((conversationId: string) => {
    if (!userId) return;
    
    // Clear timeout
    const timeout = typingTimeoutRef.current.get(conversationId);
    if (timeout) {
      clearTimeout(timeout);
      typingTimeoutRef.current.delete(conversationId);
    }
    
    // Remove from typing users
    setTypingUsers(prev => {
      const newMap = new Map(prev);
      const conversationTyping = newMap.get(conversationId);
      if (conversationTyping) {
        conversationTyping.delete(userId);
        if (conversationTyping.size === 0) {
          newMap.delete(conversationId);
        }
      }
      return newMap;
    });
  }, [userId]);

  // Assign stopTyping to ref
  stopTypingRef.current = stopTyping;

  // Mark message as read
  const markMessageAsRead = useCallback(async (messageId: string) => {
    if (!userId) return;
    
    try {
      await apiClient.put(`/api/messages?messageId=${messageId}&action=mark_read`, { userId });
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }, [userId]);

  // Create conversation
  const createConversation = useCallback(async (reportId: string, participants: string[]) => {
    try {
      const data = await apiClient.post('/api/conversations', { reportId, participants }) as { success: boolean; data: Conversation };
      if (data.success) {
        onConversationCreated?.(data.data);
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  }, [onConversationCreated]);

  // Refresh messages for a conversation
  const refreshMessages = useCallback(async (conversationId: string): Promise<MessageWithSender[]> => {
    try {
      const data = await apiClient.get(`/api/messages?conversationId=${conversationId}`) as { success: boolean; data: MessageWithSender[] };
      
      if (data.success) {
        const messages = data.data;
        
        // Update last timestamp
        if (messages.length > 0) {
          const latestMessage = messages[messages.length - 1];
          if (latestMessage.createdAt) {
            lastMessageTimestamps.current.set(conversationId, new Date(latestMessage.createdAt));
          }
        }
        
        return messages;
      }
      
      return [];
    } catch (error) {
      console.error('Error refreshing messages:', error);
      return [];
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    const currentTimeouts = typingTimeoutRef.current;
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      
      // Clear all typing timeouts
      currentTimeouts.forEach(timeout => clearTimeout(timeout));
      currentTimeouts.clear();
    };
  }, []);

  return {
    isConnected,
    onlineUsers,
    typingUsers,
    sendMessage,
    joinConversation,
    leaveConversation,
    startTyping,
    stopTyping,
    markMessageAsRead,
    createConversation,
    refreshMessages
  };
};