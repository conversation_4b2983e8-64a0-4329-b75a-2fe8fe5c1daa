"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Eye, Edit, AlertTriangle, Check, X, FileText } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'new':
            return 'bg-green-100 text-green-800';
        case 'in progress':
            return 'bg-yellow-100 text-yellow-800';
        case 'closed':
            return 'bg-gray-100 text-gray-800';
        case 'escalated':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
        case 'high':
            return 'bg-red-100 text-red-800';
        case 'medium':
            return 'bg-yellow-100 text-yellow-800';
        case 'low':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

type CaseData = {
    id: string;
    reportId: string;
    title: string;
    category: string;
    status: string;
    priority: string;
    dateSubmitted: string;
    userId?: {
        firstName?: string;
        lastName?: string;
        email?: string;
    };
};

const updateReportStatus = async (reportId: string, status: string) => {
    try {
        const response = await fetch(`/api/reports/${reportId}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status })
        });
        
        const data = await response.json();
        if (data.success) {
            toast.success(`Report ${status.toLowerCase()} successfully`);
        } else {
            toast.error(data.error || 'Failed to update report');
        }
    } catch {
        toast.error('Failed to update report status');
    }
};

const columns: ColumnDef<CaseData>[] = [
    {
        accessorKey: "id",
        header: "Case ID",
        cell: ({ row }) => {
            const case_item = row.original;
            return (
                <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">#{case_item.reportId || case_item.id}</span>
                    {case_item.priority === 'High' && (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                </div>
            );
        },
    },
    {
        accessorKey: "title",
        header: "Title",
        cell: ({ row }) => (
            <div className="max-w-[200px] truncate font-medium">
                {row.getValue("title")}
            </div>
        ),
    },
    {
        accessorKey: "category",
        header: "Category",
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => {
            const status = row.getValue("status") as string;
            return (
                <Badge variant="secondary" className={`text-xs ${getStatusColor(status)}`}>
                    {status}
                </Badge>
            );
        },
    },
    {
        accessorKey: "priority",
        header: "Priority",
        cell: ({ row }) => {
            const priority = row.getValue("priority") as string;
            return (
                <Badge variant="secondary" className={`text-xs ${getPriorityColor(priority)}`}>
                    {priority}
                </Badge>
            );
        },
    },
    {
        accessorKey: "dateSubmitted",
        header: "Date Submitted",
    },
    {
        id: "actions",
        cell: ({ row }) => {
            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateReportStatus(row.original.id, 'Under Review')}>
                            <Edit className="mr-2 h-4 w-4" />
                            Start Review
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateReportStatus(row.original.id, 'Resolved')}>
                            <Check className="mr-2 h-4 w-4" />
                            Approve
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateReportStatus(row.original.id, 'Closed')}>
                            <X className="mr-2 h-4 w-4" />
                            Reject
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        },
    },
];

export default function AdminRecentCases() {
    const { user, isAuthenticated } = useAuth();
    const [cases, setCases] = useState<CaseData[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const loadRecentCases = useCallback(async () => {
        if (!isAuthenticated || !user?.id) {
            setCases([]);
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            const response = await fetch('/api/reports?limit=8', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();

            if (data.success && Array.isArray(data.data)) {
                const transformedCases = data.data.map((report: {
                    _id: string;
                    reportId: string;
                    title: string;
                    category: string;
                    status: string;
                    priority: string;
                    createdAt: string;
                    userId?: {
                        firstName?: string;
                        lastName?: string;
                        email?: string;
                    };
                }) => ({
                    id: report._id,
                    reportId: report.reportId,
                    title: report.title,
                    category: report.category,
                    status: report.status,
                    priority: report.priority,
                    dateSubmitted: new Date(report.createdAt).toLocaleDateString(),
                    userId: report.userId
                }));
                setCases(transformedCases);
            } else {
                setCases([]);
            }
        } catch (error) {
            console.error('Error loading recent cases:', error);
            setCases([]);
        } finally {
            setIsLoading(false);
        }
    }, [isAuthenticated, user?.id]);

    useEffect(() => {
        loadRecentCases();
    }, [loadRecentCases]);

    if (isLoading) {
        return (
            <Card className="bg-white border-0 shadow-sm h-full">
                <CardHeader className="pb-0">
                    <div className="flex items-center justify-between">
                        <CardTitle className="text-base sm:text-lg font-semibold text-gray-900">
                            Recent Cases
                        </CardTitle>
                        <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                            View All
                        </Button>
                    </div>
                </CardHeader>
                <CardContent className="pt-0">
                    <div className="space-y-4">
                        {[...Array(5)].map((_, index) => (
                            <div key={index} className="flex items-center space-x-4 animate-pulse">
                                <div className="h-4 bg-gray-200 rounded w-16"></div>
                                <div className="h-4 bg-gray-200 rounded flex-1"></div>
                                <div className="h-4 bg-gray-200 rounded w-20"></div>
                                <div className="h-4 bg-gray-200 rounded w-16"></div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="bg-white border-0 shadow-sm h-full gap-0">
            <CardHeader className="pb-0">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-base sm:text-lg font-semibold text-gray-900">
                        Recent Cases
                    </CardTitle>
                    <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                        View All
                    </Button>
                </div>
            </CardHeader>
            <CardContent className="pt-0">
                {cases.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <FileText className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-900 mb-1">No cases found</h3>
                        <p className="text-xs text-gray-500">Cases will appear here once submitted</p>
                    </div>
                ) : (
                    <DataTable 
                        columns={columns} 
                        data={cases} 
                        searchKey="title"
                        searchPlaceholder="Search cases..."
                    />
                )}
            </CardContent>
        </Card>
    );
}