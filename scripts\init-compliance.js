import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

import connectDB from '../src/lib/db/mongodb.ts';
import mongoose from 'mongoose';
const { Schema, model, models } = mongoose;

async function initializeCompliance() {
  await connectDB();
  
  // Create indexes for performance
  const ConsentSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, required: true, index: true },
    consentType: { type: String, required: true },
    granted: { type: Boolean, required: true },
    timestamp: { type: Date, default: Date.now, index: true },
    ipAddress: String
  });
  
  const ArchiveSchema = new Schema({
    dataType: { type: String, index: true },
    originalId: Schema.Types.ObjectId,
    data: Schema.Types.Mixed,
    archivedAt: { type: Date, default: Date.now, index: true }
  });
  
  const AuditLogSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, index: true },
    action: String,
    timestamp: { type: Date, default: Date.now, index: true },
    details: Schema.Types.Mixed
  });
  
  // Create models (will create collections if they don't exist)
  const Consent = models.Consent || model('Consent', ConsentSchema);
  const Archive = models.Archive || model('Archive', ArchiveSchema);
  const AuditLog = models.AuditLog || model('AuditLog', AuditLogSchema);
  
  console.log('Compliance collections initialized');
  process.exit(0);
}

initializeCompliance().catch(console.error);