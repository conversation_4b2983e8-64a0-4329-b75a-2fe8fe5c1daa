// Hardcoded users for development and testing
export interface HardcodedUser {
  id: string;
  email: string;
  password: string;
  name: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'investigator' | 'whistleblower' | 'company_admin';
  companyId?: string;
  isActive: boolean;
  createdAt: Date;
  lastActive?: Date;
}

export const HARDCODED_USERS: HardcodedUser[] = [
  // TechCorp Industries Users
  {
    id: 'admin-techcorp',
    email: '<EMAIL>',
    password: 'admin123',
    name: '<PERSON>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'admin',
    companyId: 'techcorp-1',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastActive: new Date()
  },
  {
    id: 'investigator-techcorp',
    email: '<EMAIL>',
    password: 'investigator123',
    name: '<PERSON>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'investigator',
    companyId: 'techcorp-1',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastActive: new Date()
  },
  {
    id: 'whistleblower-john',
    email: '<EMAIL>',
    password: 'employee123',
    name: '<PERSON>e',
    firstName: '<PERSON>',
    lastName: 'Doe',
    role: 'whistleblower',
    companyId: 'techcorp-1',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastActive: new Date()
  },
  {
    id: 'whistleblower-jane',
    email: '<EMAIL>',
    password: 'whistleblower123',
    name: 'Jane Smith',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'whistleblower',
    companyId: 'techcorp-1',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastActive: new Date()
  },
  // Global Manufacturing Users
  {
    id: 'admin-global',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Emily Rodriguez',
    firstName: 'Emily',
    lastName: 'Rodriguez',
    role: 'admin',
    companyId: 'global-1',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastActive: new Date()
  },
  {
    id: 'investigator-global',
    email: '<EMAIL>',
    password: 'investigator123',
    name: 'David Thompson',
    firstName: 'David',
    lastName: 'Thompson',
    role: 'investigator',
    companyId: 'global-1',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastActive: new Date()
  },
  {
    id: 'whistleblower-robert',
    email: '<EMAIL>',
    password: 'whistleblower123',
    name: 'Robert Wilson',
    firstName: 'Robert',
    lastName: 'Wilson',
    role: 'whistleblower',
    companyId: 'global-1',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastActive: new Date()
  },
  {
    id: 'whistleblower-lisa',
    email: '<EMAIL>',
    password: 'whistleblower123',
    name: 'Lisa Brown',
    firstName: 'Lisa',
    lastName: 'Brown',
    role: 'whistleblower',
    companyId: 'global-1',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastActive: new Date()
  }
];

// Utility functions for managing hardcoded users
export const getUserByEmail = (email: string): HardcodedUser | undefined => {
  return HARDCODED_USERS.find(user => user.email === email);
};

export const getUserById = (id: string): HardcodedUser | undefined => {
  return HARDCODED_USERS.find(user => user.id === id);
};

export const validateCredentials = (email: string, password: string): HardcodedUser | null => {
  const user = HARDCODED_USERS.find(u => 
    u.email === email && u.password === password && u.isActive
  );
  return user || null;
};

export const getUsersByRole = (role: HardcodedUser['role']): HardcodedUser[] => {
  return HARDCODED_USERS.filter(user => user.role === role && user.isActive);
};

export const getAllUsers = (): HardcodedUser[] => {
  return HARDCODED_USERS.filter(user => user.isActive);
};

// For display purposes - hide passwords
export const getUsersForDisplay = () => {
  return HARDCODED_USERS.map((user) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  });
};

// Login credentials for reference
export const LOGIN_CREDENTIALS = [
  { email: '<EMAIL>', password: 'admin123', role: 'Admin', company: 'TechCorp Industries' },
  { email: '<EMAIL>', password: 'investigator123', role: 'Investigator', company: 'TechCorp Industries' },
  { email: '<EMAIL>', password: 'employee123', role: 'Whistleblower', company: 'TechCorp Industries' },
  { email: '<EMAIL>', password: 'whistleblower123', role: 'Whistleblower', company: 'TechCorp Industries' },
  { email: '<EMAIL>', password: 'admin123', role: 'Admin', company: 'Global Manufacturing Ltd' },
  { email: '<EMAIL>', password: 'investigator123', role: 'Investigator', company: 'Global Manufacturing Ltd' },
  { email: '<EMAIL>', password: 'whistleblower123', role: 'Whistleblower', company: 'Global Manufacturing Ltd' },
  { email: '<EMAIL>', password: 'whistleblower123', role: 'Whistleblower', company: 'Global Manufacturing Ltd' },
];