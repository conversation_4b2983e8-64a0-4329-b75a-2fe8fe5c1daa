import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import jwt from 'jsonwebtoken';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    role: 'admin' | 'investigator' | 'whistleblower';
    companyId?: string;
    firstName?: string;
    lastName?: string;
  };
}

export interface AuthResult {
  success: boolean;
  user?: AuthenticatedRequest['user'];
  error?: string;
}

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

export async function authenticateRequest(request: NextRequest): Promise<AuthResult> {
  try {
    // Check for Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { success: false, error: 'No valid authorization header found' };
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify JWT token
    let decoded: { userId: string; [key: string]: unknown };
    try {
      decoded = jwt.verify(token, JWT_SECRET) as { userId: string; [key: string]: unknown };
    } catch {
      return { success: false, error: 'Invalid or expired token' };
    }

    // Get user from database
    let user = await DataService.getUserById(decoded.userId);
    
    // If user not found by ID, try to find by email (for hardcoded users)
    if (!user && decoded.email && typeof decoded.email === 'string') {
      user = await DataService.getUserByEmail(decoded.email);
    }
    
    if (!user) {
      return { success: false, error: 'User not found' };
    }

    // Check if user is active
    if (!user.isActive) {
      return { success: false, error: 'Account is inactive' };
    }

    // Check if account is locked using the model method (which auto-unlocks expired locks)
    if (user.isAccountLocked && user.isAccountLocked()) {
      return { success: false, error: 'Account is temporarily locked' };
    }

    return {
      success: true,
      user: {
        id: user._id.toString(),
        email: user.email,
        role: user.role as 'admin' | 'investigator' | 'whistleblower',
        companyId: user.companyId?.toString(),
        firstName: user.firstName,
        lastName: user.lastName
      }
    };
  } catch (error: unknown) {
    console.error('Authentication error:', error);

    // Handle version errors specifically
    if (typeof error === 'object' && error && (error as { name?: string }).name === 'VersionError') {
      console.log('Version error in authentication, retrying with fresh user data');
      try {
        // Without a safe userId in this scope, we cannot refetch reliably; return failure
        return { success: false, error: 'Authentication failed' };
      } catch (retryError) {
        console.error('Retry authentication error:', retryError);
        return { success: false, error: 'Authentication failed' };
      }
    }

    return { success: false, error: 'Authentication failed' };
  }
}

export function generateToken(userId: string, userRole?: string, companyId?: string, email?: string): string {
  return jwt.sign(
    {
      userId,
      id: userId, // Include both for compatibility
      role: userRole,
      companyId,
      email,
      iat: Math.floor(Date.now() / 1000)
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
}

export function createAuthResponse(error: string, status: number = 401): NextResponse {
  return NextResponse.json(
    { success: false, error },
    { status }
  );
}

// Middleware wrapper for API routes
export function withAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  options: { 
    roles?: ('admin' | 'investigator' | 'whistleblower')[];
    requireCompany?: boolean;
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const authResult = await authenticateRequest(request);
    
    if (!authResult.success) {
      return createAuthResponse(authResult.error || 'Authentication failed');
    }

    // Check role permissions
    if (options.roles && !options.roles.includes(authResult.user!.role)) {
      return createAuthResponse('Insufficient permissions', 403);
    }

    // Check company requirement
    if (options.requireCompany && !authResult.user!.companyId) {
      return createAuthResponse('Company association required', 403);
    }

    // Add user to request
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = authResult.user;

    return handler(authenticatedRequest);
  };
}

// Company isolation middleware
export function withCompanyIsolation(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>
) {
  return withAuth(async (request: AuthenticatedRequest) => {
    // Ensure user has company association for data isolation
    if (!request.user?.companyId && request.user?.role !== 'admin') {
      return createAuthResponse('Company association required for data access', 403);
    }

    return handler(request);
  }, { requireCompany: false }); // We handle company check manually above
}