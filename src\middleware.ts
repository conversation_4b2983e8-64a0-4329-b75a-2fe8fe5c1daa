import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { IPAnonymizer } from '@/lib/security/ipAnonymization';

// Define protected routes and their required roles
const protectedRoutes = {
  '/dashboard/admin': 'admin',
  '/dashboard/whistleblower': 'whistleblower',
  '/dashboard/anonymous': null, // No role required, just authentication
};

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/login/admin',
  '/login/whistleblower',
  '/signup',
  '/forgot-password',
  '/reset-password',
  '/api/auth/signup',
  '/api/auth/login',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Create response with security headers
  const response = NextResponse.next();
  
  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // Anonymize IP for logging
  const fwd = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip');
  const clientIP = fwd?.split(',')[0]?.trim() || 'unknown';
  const anonymizedIP = IPAnonymizer.anonymizeIP(clientIP);
  response.headers.set('X-Anonymized-IP', anonymizedIP);
  
  // Allow public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return response;
  }

  // Allow API routes (except auth routes which are handled above)
  if (pathname.startsWith('/api/')) {
    return response;
  }

  // Allow static files
  if (pathname.startsWith('/_next/') || pathname.includes('.')) {
    return response;
  }

  // Check for authentication token in cookies or headers
  const token = request.cookies.get('auth_token')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '');

  // Also check for session data in cookies
  const sessionData = request.cookies.get('user_session')?.value;

  // If no token or session, redirect to appropriate login page
  if (!token && !sessionData) {
    const loginUrl = getLoginUrl(pathname);
    const url = request.nextUrl.clone();
    url.pathname = loginUrl;
    url.searchParams.set('redirect', pathname);
    return NextResponse.redirect(url);
  }

  // For protected routes, check role requirements
  const requiredRole = getRequiredRole(pathname);
  if (requiredRole && token) {
    try {
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key-change-in-production');
      
      if (decoded.role !== requiredRole) {
        const correctUrl = decoded.role === 'admin' ? '/dashboard/admin' : '/dashboard/whistleblower';
        const url = request.nextUrl.clone();
        url.pathname = correctUrl;
        return NextResponse.redirect(url);
      }
    } catch (error) {
      // Invalid token, redirect to login
      const loginUrl = getLoginUrl(pathname);
      const url = request.nextUrl.clone();
      url.pathname = loginUrl;
      return NextResponse.redirect(url);
    }
  }

  return response;
}

function getLoginUrl(pathname: string): string {
  // Determine which login page to redirect to based on the path
  if (pathname.startsWith('/dashboard/admin')) {
    return '/login/admin';
  }
  if (pathname.startsWith('/dashboard/whistleblower') || pathname.startsWith('/dashboard/anonymous')) {
    return '/login/whistleblower';
  }
  // Default to admin login
  return '/login/admin';
}

function getRequiredRole(pathname: string): string | null {
  for (const [route, role] of Object.entries(protectedRoutes)) {
    if (pathname.startsWith(route)) {
      return role;
    }
  }
  return null;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
