import { NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import User from '@/lib/db/models/User';

export const runtime = 'nodejs';

export async function GET() {
  try {
    await connectDB();
    
    // Get all users (without passwords for security)
    const users = await User.find({}, {
      email: 1,
      firstName: 1,
      lastName: 1,
      role: 1,
      isActive: 1,
      createdAt: 1,
      lastLogin: 1,
      companyId: 1,
      passwordNeedsMigration: 1,
      passwordHashAlgorithm: 1
    }).limit(20);
    
    const userCount = await User.countDocuments();
    
    return NextResponse.json({
      success: true,
      data: {
        totalUsers: userCount,
        users: users.map(user => ({
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          isActive: user.isActive,
          createdAt: user.createdAt,
          lastLogin: user.lastLogin,
          companyId: user.companyId,
          passwordNeedsMigration: user.passwordNeedsMigration,
          passwordHashAlgorithm: user.passwordHashAlgorithm
        }))
      }
    });
  } catch (error) {
    console.error('Debug users API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch users',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
