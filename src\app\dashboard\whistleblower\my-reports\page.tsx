"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { format } from "date-fns";
import Header from "@/components/dashboard-components/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  B<PERSON>crumbLink,
  BreadcrumbList,
  B<PERSON><PERSON><PERSON>bPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import ReportsFilters from "@/components/dashboard-components/whistleblower/my-reports/ReportsFilters";
import { DataTable } from "@/components/dashboard-components/whistleblower/my-reports/data-table";
import { useColumns } from "@/components/dashboard-components/whistleblower/my-reports/columns";
import RecentActivity from "@/components/dashboard-components/whistleblower/my-reports/RecentActivity";
import BulkActions from "@/components/dashboard-components/whistleblower/my-reports/BulkActions";
import HelpResources from "@/components/dashboard-components/whistleblower/my-reports/HelpResources";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  Plus,
  RefreshCw,
  ArrowDownToLine,
  Share2,
  Printer,
  Archive,
  Trash2,
  Home
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
// import DataService from "@/lib/db/dataService";
import { ReportData } from "@/lib/types";
import { useAuth } from "@/hooks/useAuth";
import { useDashboardData } from "@/hooks/useDashboardData";

export default function MyReportsPage() {
  const router = useRouter();
  const { } = useAuth();
  const dashboardData = useDashboardData();
  const columns = useColumns();
  // Use centralized data instead of local state
  const reportsData = dashboardData.reports;
  const activityItems = dashboardData.activities;
  const loading = dashboardData.isLoading;
  const error = dashboardData.error;
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [selectedReports, setSelectedReports] = useState<ReportData[]>([]);
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();
  const [dateValidationError, setDateValidationError] = useState<string | null>(null);
  const [bulkActionInProgress, setBulkActionInProgress] = useState(false);

  // Dialog states
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showMoreOptionsDialog, setShowMoreOptionsDialog] = useState(false);
  const [selectedReportForAction] = useState<string | null>(null);

  // Data is now loaded centrally via useDashboardData hook

  // Calculate status counts
  const statusCounts = useMemo(() => ({
    all: reportsData.length,
    drafts: reportsData.filter(report => report.status === "Draft").length,
    open: reportsData.filter(report => report.status === "New").length,
    underReview: reportsData.filter(report => report.status === "Under Review").length,
    awaitingResponse: reportsData.filter(report => report.status === "Awaiting Response").length,
    resolved: reportsData.filter(report => report.status === "Resolved").length,
  }), [reportsData]);

  // Enhanced filtering with sorting
  const filteredAndSortedReports = useMemo(() => {
    const filtered = reportsData.filter(report => {
      const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.id.toLowerCase().includes(searchTerm.toLowerCase());

      let matchesTab = true;
      switch (activeTab) {
        case "drafts":
          matchesTab = report.status === "Draft";
          break;
        case "open":
          matchesTab = report.status === "New";
          break;
        case "underReview":
          matchesTab = report.status === "Under Review";
          break;
        case "awaitingResponse":
          matchesTab = report.status === "Awaiting Response";
          break;
        case "resolved":
          matchesTab = report.status === "Resolved";
          break;
        case "all":
        default:
          matchesTab = true;
          break;
      }

      const matchesPriority = priorityFilter === "all" || report.priority === priorityFilter;
      const matchesCategory = categoryFilter === "all" || report.category === categoryFilter;

      let matchesDateRange = true;
      if (fromDate || toDate) {
        const reportDate = new Date(report.dateSubmitted);
        if (fromDate && reportDate < fromDate) {
          matchesDateRange = false;
        }
        if (toDate && reportDate > toDate) {
          matchesDateRange = false;
        }
      }

      return matchesSearch && matchesTab && matchesPriority && matchesCategory && matchesDateRange;
    });

    return filtered.sort((a, b) => new Date(b.dateSubmitted).getTime() - new Date(a.dateSubmitted).getTime());
  }, [reportsData, searchTerm, activeTab, priorityFilter, categoryFilter, fromDate, toDate]);

  // Get unique values for filters
  const categories = useMemo(() => [...new Set(reportsData.map(report => report.category))], [reportsData]);
  const priorities = useMemo(() => [...new Set(reportsData.map(report => report.priority))], [reportsData]);



  const getStatusTextColor = useCallback((status: string) => {
    switch (status) {
      case 'Resolved':
        return '#16A34A';
      case 'New':
        return '#2563EB';
      case 'Under Review':
        return '#EA580C';
      default:
        return '#6B7280';
    }
  }, []);

  // Filter helpers
  const hasActiveFilters = useCallback(() => {
    return searchTerm !== "" ||
      activeTab !== "all" ||
      priorityFilter !== "all" ||
      categoryFilter !== "all" ||
      fromDate !== undefined ||
      toDate !== undefined;
  }, [searchTerm, activeTab, priorityFilter, categoryFilter, fromDate, toDate]);

  const getActiveFilters = useCallback(() => {
    const filters = [];
    if (searchTerm) {
      filters.push({ type: "search", label: `Search: "${searchTerm}"`, value: searchTerm });
    }
    if (activeTab !== "all") {
      const tabLabels = {
        "open": "Status: Open",
        "underReview": "Status: Under Review",
        "awaitingResponse": "Status: Awaiting Response",
        "resolved": "Status: Resolved"
      };
      filters.push({ type: "tab", label: tabLabels[activeTab as keyof typeof tabLabels], value: activeTab });
    }
    if (priorityFilter !== "all") {
      filters.push({ type: "priority", label: `Priority: ${priorityFilter}`, value: priorityFilter });
    }
    if (categoryFilter !== "all") {
      filters.push({ type: "category", label: `Category: ${categoryFilter}`, value: categoryFilter });
    }
    if (fromDate) {
      filters.push({ type: "fromDate", label: `From: ${format(fromDate, "PPP")}`, value: fromDate });
    }
    if (toDate) {
      filters.push({ type: "toDate", label: `To: ${format(toDate, "PPP")}`, value: toDate });
    }
    return filters;
  }, [searchTerm, activeTab, priorityFilter, categoryFilter, fromDate, toDate]);

  // Event handlers
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);
  }, []);

  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
  }, []);

  const handleFromDateChange = useCallback((date: Date | undefined) => {
    setDateValidationError(null);
    if (date) {
      const today = new Date();
      today.setHours(23, 59, 59, 999);
      if (date > today) {
        setDateValidationError("From date cannot be in the future");
        return;
      }
      if (toDate && date > toDate) {
        setDateValidationError("From date cannot be after To date");
        setToDate(undefined);
      }
    }
    setFromDate(date);
  }, [toDate]);

  const handleToDateChange = useCallback((date: Date | undefined) => {
    setDateValidationError(null);
    if (date) {
      const today = new Date();
      today.setHours(23, 59, 59, 999);
      if (date > today) {
        setDateValidationError("To date cannot be in the future");
        return;
      }
      if (fromDate && date < fromDate) {
        setDateValidationError("To date cannot be before From date");
        setFromDate(undefined);
      }
    }
    setToDate(date);
  }, [fromDate]);

  const handleCategoryChange = useCallback((value: string) => {
    setCategoryFilter(value);
  }, []);

  const handlePriorityChange = useCallback((value: string) => {
    setPriorityFilter(value);
  }, []);

  const clearAllFilters = useCallback(() => {
    setSearchTerm("");
    setActiveTab("all");
    setPriorityFilter("all");
    setCategoryFilter("all");
    setFromDate(undefined);
    setToDate(undefined);
    setDateValidationError(null);
  }, []);

  const removeFilter = useCallback((filterType: string) => {
    switch (filterType) {
      case "search":
        setSearchTerm("");
        break;
      case "tab":
        setActiveTab("all");
        break;
      case "priority":
        setPriorityFilter("all");
        break;
      case "category":
        setCategoryFilter("all");
        break;
      case "fromDate":
        setFromDate(undefined);
        setDateValidationError(null);
        break;
      case "toDate":
        setToDate(undefined);
        setDateValidationError(null);
        break;
    }
  }, []);

  // Selection handlers
  const handleSelectionChange = useCallback((selectedRows: ReportData[]) => {
    setSelectedReports(selectedRows);
  }, []);

  // Bulk action handlers
  const generateCSV = (data: ReportData[]) => {
    const headers = ['Report ID', 'Title', 'Status', 'Priority', 'Category', 'Date Submitted', 'Last Updated', 'Progress'];
    const csvRows = [
      headers.join(','),
      ...data.map(report => [
        `"${report.id}"`,
        `"${report.title.replace(/"/g, '""')}"`,
        `"${report.status}"`,
        `"${report.priority}"`,
        `"${report.category}"`,
        `"${report.dateSubmitted}"`,
        `"${report.lastUpdated}"`,
        `"${report.progressPercentage || 0}%"`
      ].join(','))
    ];
    return csvRows.join('\n');
  };

  const downloadFile = (content: string, filename: string, contentType: string) => {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const handleBulkExport = async (format: 'csv' | 'pdf') => {
    setBulkActionInProgress(true);
    try {
      const dataToExport = selectedReports;
      const filename = `selected-reports-${new Date().toISOString().split('T')[0]}`;
      
      if (format === 'csv') {
        const csvContent = generateCSV(dataToExport);
        downloadFile(csvContent, `${filename}.csv`, 'text/csv');
        toast.success(`Successfully exported ${dataToExport.length} report(s) to CSV`);
      } else {
        toast.info('PDF export functionality will be implemented');
      }
    } catch {
      toast.error('Export failed. Please try again.');
    } finally {
      setBulkActionInProgress(false);
    }
  };

  const handleBulkArchive = async () => {
    if (selectedReports.length === 0) {
      toast.error('Please select reports to archive');
      return;
    }
    setShowArchiveDialog(true);
  };

  const confirmBulkArchive = async () => {
    setBulkActionInProgress(true);
    setShowArchiveDialog(false);
    try {
      toast.success(`${selectedReports.length} report(s) archived successfully`);
      setSelectedReports([]);
    } catch {
      toast.error('Archive failed. Please try again.');
    } finally {
      setBulkActionInProgress(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedReports.length === 0) {
      toast.error('Please select reports to delete');
      return;
    }
    setShowDeleteDialog(true);
  };

  const confirmBulkDelete = async () => {
    setBulkActionInProgress(true);
    setShowDeleteDialog(false);
    try {
      toast.success(`${selectedReports.length} report(s) deleted successfully`);
      setSelectedReports([]);
    } catch {
      toast.error('Delete failed. Please try again.');
    } finally {
      setBulkActionInProgress(false);
    }
  };

  const handleDuplicate = () => {
    if (selectedReports.length === 1) {
      const reportId = selectedReports[0].id;
      router.push(`/dashboard/whistleblower/new-report?duplicate=${reportId}`);
    }
  };

  const handleShare = () => {
    if (selectedReports.length > 0) {
      const reportIds = selectedReports.map(r => r.id).join(',');
      const shareUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/dashboard/whistleblower/reports/shared?ids=${reportIds}`;
      navigator.clipboard.writeText(shareUrl).then(() => {
        toast.success('Share link copied to clipboard!', {
          description: 'Recipients will need appropriate permissions to view these reports.'
        });
      }).catch(() => {
        const userCopied = prompt('Copy this share link:', shareUrl);
        if (userCopied !== null) {
          toast.info('Share link provided');
        }
      });
    }
  };

  const handlePrint = () => {
    toast.info('Print functionality will be implemented');
  };

  // Memoize the ReportsFilters component
  const memoizedReportsFilters = useMemo(() => (
    <ReportsFilters
      searchTerm={searchTerm}
      onSearchChange={handleSearchChange}
      priorityFilter={priorityFilter}
      onPriorityChange={handlePriorityChange}
      categoryFilter={categoryFilter}
      onCategoryChange={handleCategoryChange}
      fromDate={fromDate}
      onFromDateChange={handleFromDateChange}
      toDate={toDate}
      onToDateChange={handleToDateChange}
      dateValidationError={dateValidationError}
      categories={categories}
      priorities={priorities}
      hasActiveFilters={hasActiveFilters()}
      activeFilters={getActiveFilters()}
      onRemoveFilter={removeFilter}
      onClearAllFilters={clearAllFilters}
    />
  ), [
    searchTerm,
    priorityFilter,
    categoryFilter,
    fromDate,
    toDate,
    dateValidationError,
    categories,
    priorities,
    hasActiveFilters,
    getActiveFilters,
    handleSearchChange,
    handlePriorityChange,
    handleCategoryChange,
    handleFromDateChange,
    handleToDateChange,
    removeFilter,
    clearAllFilters
  ]);

  return (
    <>
      <div className="w-full h-full">
        <Header />
        <main id="main-content" className="bg-gray-50 min-h-screen">
          {/* Header Section */}
          <header className="px-4 sm:px-6 py-4 w-full flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 lg:gap-0 bg-white">
            <div className="flex-1">
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link href="/dashboard/whistleblower" className="flex items-center gap-1">
                        <Home className="w-4 h-4" />
                        Dashboard
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>My Reports</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
              <h1 className="text-2xl sm:text-3xl font-bold text-[#1F2937]">My Reports</h1>
              <p className="text-[#6B7280] mt-2 text-sm sm:text-base">View, track, and manage all your whistleblower reports in one place</p>
            </div>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
              <Button
                variant={"outline"}
                onClick={() => router.refresh()}
                disabled={loading}
                className="py-4 sm:py-6 px-4 text-sm sm:text-base text-[#374151] w-full sm:w-auto"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                variant={"outline"}
                onClick={() => handleBulkExport('csv')}
                disabled={bulkActionInProgress}
                className="py-4 sm:py-6 px-4 text-sm sm:text-base text-[#374151] w-full sm:w-auto"
              >
                <ArrowDownToLine className="w-4 h-4 mr-2" />
                {bulkActionInProgress ? 'Exporting...' : 'Export All'}
              </Button>
              <Link href="/dashboard/whistleblower/new-report" className="w-full sm:w-auto">
                <Button className="bg-[#1E4841] hover:bg-[#2A5D54] text-white text-sm sm:text-base py-4 sm:py-6 px-4 w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  New Report
                </Button>
              </Link>
            </div>
          </header>

          {/* Main Content Layout */}
          <div className="flex flex-col gap-6">
            {loading ? (
              <div className="flex flex-col gap-6">
                <div className="bg-white w-full rounded-none border-b border-t p-6">
                  <div className="animate-pulse">
                    <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
                <Card className="mx-6">
                  <CardContent className="p-6">
                    <div className="animate-pulse space-y-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : error ? (
              <Card className="mx-6">
                <CardContent className="p-6 text-center">
                  <p className="text-red-600 mb-4">Error loading reports: {error}</p>
                  <Button onClick={() => router.refresh()} variant="outline">
                    Retry
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <>
                {/* Tab Navigation */}
                <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full gap-0">
                  <div className="bg-white w-full rounded-none border-b border-t">
                    <TabsList className="w-fit flex bg-white p-0 h-auto pl-6">
                      <TabsTrigger value="all" className="data-[state=active]:bg-transparent data-[state=active]:border-b-3 data-[state=active]:border-[#1E4841] data-[state=active]:text-[#1E4841] data-[state=active]:shadow-none text-[#6B7271] data-[state=active]:text-base data-[state=active]:font-medium rounded-none border-0 shadow-none py-3 px-4 text-base font-normal w-fit">
                        All Reports ({statusCounts.all})
                      </TabsTrigger>
                      <TabsTrigger value="drafts" className="data-[state=active]:bg-transparent data-[state=active]:border-b-3 data-[state=active]:border-[#1E4841] data-[state=active]:text-[#1E4841] data-[state=active]:shadow-none text-[#6B7271] data-[state=active]:text-base data-[state=active]:font-medium rounded-none border-0 shadow-none py-3 px-4 text-base font-normal w-fit">
                        Drafts ({statusCounts.drafts})
                      </TabsTrigger>
                      <TabsTrigger value="open" className="data-[state=active]:bg-transparent data-[state=active]:border-b-3 data-[state=active]:border-[#1E4841] data-[state=active]:text-[#1E4841] data-[state=active]:shadow-none text-[#6B7271] data-[state=active]:text-base data-[state=active]:font-medium rounded-none border-0 shadow-none py-3 px-4 text-base font-normal w-fit">
                        Open ({statusCounts.open})
                      </TabsTrigger>
                      <TabsTrigger value="underReview" className="data-[state=active]:bg-transparent data-[state=active]:border-b-3 data-[state=active]:border-[#1E4841] data-[state=active]:text-[#1E4841] data-[state=active]:shadow-none text-[#6B7271] data-[state=active]:text-base data-[state=active]:font-medium rounded-none border-0 shadow-none py-3 px-4 text-base font-normal w-fit">
                        Under Review ({statusCounts.underReview})
                      </TabsTrigger>
                      <TabsTrigger value="awaitingResponse" className="data-[state=active]:bg-transparent data-[state=active]:border-b-3 data-[state=active]:border-[#1E4841] data-[state=active]:text-[#1E4841] data-[state=active]:shadow-none text-[#6B7271] data-[state=active]:text-base data-[state=active]:font-medium rounded-none border-0 shadow-none py-3 px-4 text-base font-normal w-fit">
                        Awaiting Response ({statusCounts.awaitingResponse})
                      </TabsTrigger>
                      <TabsTrigger value="resolved" className="data-[state=active]:bg-transparent data-[state=active]:border-b-4 data-[state=active]:border-[#1E4841] data-[state=active]:text-[#1E4841] data-[state=active]:shadow-none text-[#6B7271] data-[state=active]:text-base data-[state=active]:font-medium rounded-none border-0 shadow-none py-3 px-4 text-base font-normal w-fit">
                        Resolved ({statusCounts.resolved})
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent value={activeTab}>
                    {memoizedReportsFilters}

                    {/* Reports Table */}
                    <Card className="mx-4 sm:mx-6 mt-6 gap-0">
                      <CardHeader className="px-4">
                        <CardTitle className="text-base sm:text-lg mb-0 pb-0">Reports</CardTitle>
                      </CardHeader>
                      <CardContent className="p-4">
                        <DataTable
                          columns={columns}
                          data={filteredAndSortedReports}
                          onSelectionChange={handleSelectionChange}
                        />
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>

                <RecentActivity 
                  activityItems={activityItems} 
                  getStatusTextColor={getStatusTextColor} 
                />

                <div className="flex flex-col xl:flex-row gap-4 sm:gap-6 mx-4 sm:mx-6">
                  <BulkActions
                    selectedReports={selectedReports.map(r => r.id)}
                    bulkActionInProgress={bulkActionInProgress}
                    onBulkExport={handleBulkExport}
                    onBulkArchive={handleBulkArchive}
                    onBulkDelete={handleBulkDelete}
                    onDuplicate={handleDuplicate}
                    onShare={handleShare}
                    onPrint={handlePrint}
                  />
                  <HelpResources />
                </div>
              </>
            )}
          </div>
        </main>
      </div>

      {/* Dialogs */}
      <AlertDialog open={showArchiveDialog} onOpenChange={setShowArchiveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Archive Reports</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to archive {selectedReports.length} report(s)?
              Archived reports can be restored later if needed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkArchive}
              className="bg-[#1E4841] hover:bg-[#2A5D54]"
            >
              Archive Reports
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Reports</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedReports.length} report(s)?
              This action cannot be undone and all data will be permanently lost.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Reports
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={showMoreOptionsDialog} onOpenChange={setShowMoreOptionsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Report Actions</DialogTitle>
            <DialogDescription>
              Choose an action for report {selectedReportForAction}
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 gap-3 py-4">
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => {
                if (selectedReportForAction) {
                  const reportData = filteredAndSortedReports.find(r => r.id === selectedReportForAction);
                  if (reportData) {
                    const csvContent = generateCSV([reportData]);
                    downloadFile(csvContent, `report-${selectedReportForAction}.csv`, 'text/csv');
                    toast.success('Report downloaded successfully');
                  }
                }
                setShowMoreOptionsDialog(false);
              }}
            >
              <ArrowDownToLine className="w-4 h-4 mr-2" />
              Download Report
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => {
                if (selectedReportForAction) {
                  const shareUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/dashboard/whistleblower/reports/shared?ids=${selectedReportForAction}`;
                  navigator.clipboard.writeText(shareUrl).then(() => {
                    toast.success('Share link copied to clipboard!');
                  });
                }
                setShowMoreOptionsDialog(false);
              }}
            >
              <Share2 className="w-4 h-4 mr-2" />
              Share Report
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => {
                toast.info('Print functionality will be implemented');
                setShowMoreOptionsDialog(false);
              }}
            >
              <Printer className="w-4 h-4 mr-2" />
              Print Report
            </Button>
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => {
                if (selectedReportForAction) {
                  const reportData = filteredAndSortedReports.find(r => r.id === selectedReportForAction);
                  if (reportData) {
                    setSelectedReports([reportData]);
                    setShowMoreOptionsDialog(false);
                    setShowArchiveDialog(true);
                  }
                }
              }}
            >
              <Archive className="w-4 h-4 mr-2" />
              Archive Report
            </Button>
            <Button
              variant="outline"
              className="justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={() => {
                if (selectedReportForAction) {
                  const reportData = filteredAndSortedReports.find(r => r.id === selectedReportForAction);
                  if (reportData) {
                    setSelectedReports([reportData]);
                    setShowMoreOptionsDialog(false);
                    setShowDeleteDialog(true);
                  }
                }
              }}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Report
            </Button>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowMoreOptionsDialog(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
